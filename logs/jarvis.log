2025-07-12 10:53:53 | INFO     | __main__:initialize:23 | Initializing Jarvis AI Assistant...
2025-07-12 10:53:53 | INFO     | __main__:initialize:47 | Jarvis initialization complete!
2025-07-12 10:53:53 | INFO     | __main__:start:54 | <PERSON> is now listening...
2025-07-12 10:53:53 | INFO     | __main__:start:55 | Say 'jarvis' to activate
2025-07-12 10:54:08 | INFO     | __main__:signal_handler:87 | Received signal 2
2025-07-12 10:54:08 | INFO     | __main__:shutdown:69 | Shutting down Jarvis...
2025-07-12 10:54:08 | INFO     | __main__:shutdown:79 | <PERSON> shutdown complete
2025-07-12 10:57:08 | INFO     | __main__:main:45 | 🚀 Starting OpenAI API test...
2025-07-12 10:57:08 | INFO     | __main__:test_openai_api:14 | 🔑 Testing OpenAI API key...
2025-07-12 10:57:08 | INFO     | __main__:test_openai_api:15 | API Key: sk-ijklqrst5678uvwxi...
2025-07-12 10:57:09 | ERROR    | __main__:test_openai_api:40 | ❌ OpenAI API test failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:57:09 | ERROR    | __main__:main:52 | 💥 API test failed!
2025-07-12 10:58:24 | INFO     | __main__:initialize:24 | Initializing Jarvis AI Assistant...
2025-07-12 10:58:24 | INFO     | jarvis.modules.voice:initialize:28 | Initializing Voice Module...
2025-07-12 10:58:24 | INFO     | jarvis.modules.voice:_test_openai_connection:60 | Testing OpenAI API connection...
2025-07-12 10:58:27 | ERROR    | jarvis.modules.voice:_test_openai_connection:77 | ❌ OpenAI API connection failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:58:27 | WARNING  | jarvis.modules.voice:initialize:37 | OpenAI API not available: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:58:27 | INFO     | jarvis.modules.voice:initialize:38 | 🔄 Continuing with local speech recognition only
2025-07-12 10:58:29 | INFO     | jarvis.modules.voice:initialize:47 | Calibrating microphone for ambient noise...
2025-07-12 10:58:30 | INFO     | jarvis.modules.voice:initialize:50 | Voice Module initialized successfully
2025-07-12 10:58:30 | INFO     | __main__:initialize:46 | Module VoiceModule initialized successfully
2025-07-12 10:58:30 | INFO     | __main__:initialize:52 | Jarvis initialization complete!
2025-07-12 10:58:30 | INFO     | __main__:start:59 | Jarvis is now listening...
2025-07-12 10:58:30 | INFO     | __main__:start:60 | Say 'jarvis' to activate
2025-07-12 10:58:49 | INFO     | __main__:signal_handler:111 | Received signal 2
2025-07-12 10:58:49 | INFO     | __main__:shutdown:77 | Shutting down Jarvis...
2025-07-12 10:58:49 | INFO     | jarvis.modules.voice:stop_listening:99 | 🔇 Stopped listening
2025-07-12 10:58:49 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 10:58:49 | INFO     | __main__:shutdown:87 | Jarvis shutdown complete
2025-07-12 10:59:43 | INFO     | __main__:initialize:24 | Initializing Jarvis AI Assistant...
2025-07-12 10:59:43 | INFO     | jarvis.modules.voice:initialize:28 | Initializing Voice Module...
2025-07-12 10:59:43 | INFO     | jarvis.modules.voice:_test_openai_connection:61 | Testing OpenAI API connection...
2025-07-12 10:59:44 | ERROR    | jarvis.modules.voice:_test_openai_connection:78 | ❌ OpenAI API connection failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:59:44 | WARNING  | jarvis.modules.voice:initialize:37 | OpenAI API not available: Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-ijklq*******************************qrst. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-07-12 10:59:44 | INFO     | jarvis.modules.voice:initialize:38 | 🔄 Continuing with local speech recognition only
2025-07-12 10:59:44 | INFO     | jarvis.modules.voice:initialize:47 | Calibrating microphone for ambient noise...
2025-07-12 10:59:45 | INFO     | jarvis.modules.voice:initialize:50 | Voice Module initialized successfully
2025-07-12 10:59:45 | INFO     | __main__:initialize:46 | Module VoiceModule initialized successfully
2025-07-12 10:59:45 | INFO     | __main__:initialize:52 | Jarvis initialization complete!
2025-07-12 10:59:45 | INFO     | __main__:start:59 | Jarvis is now listening...
2025-07-12 10:59:45 | INFO     | __main__:start:60 | Say 'jarvis' to activate
2025-07-12 10:59:45 | INFO     | jarvis.modules.voice:start_listening:91 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:00:00 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:09 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:15 | ERROR    | jarvis.modules.voice:_listen_loop:126 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:00:35 | INFO     | __main__:signal_handler:111 | Received signal 2
2025-07-12 11:00:35 | INFO     | __main__:shutdown:77 | Shutting down Jarvis...
2025-07-12 11:00:35 | INFO     | jarvis.modules.voice:stop_listening:100 | 🔇 Stopped listening
2025-07-12 11:00:35 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:00:35 | INFO     | __main__:shutdown:87 | Jarvis shutdown complete
2025-07-12 11:05:10 | INFO     | __main__:test_app_manager:13 | 🚀 Starting App Manager test...
2025-07-12 11:05:10 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:05:12 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:05:13 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:05:13 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:05:13 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:28 | ✅ Modules initialized successfully
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:31 | 
📱 Test 1: Listing running applications...
2025-07-12 11:05:13 | INFO     | jarvis.modules.app_manager:list_running_applications:218 | 📱 Found 5 running applications
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:35 | ✅ Found 5 running applications:
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Telegram
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Safari
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Terminal
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Finder
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:37 |    • Electron
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:44 | 
🧠 Test 2: Command parsing...
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'открой калькулятор' -> app_open (entities: ['калькулятор'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'запусти сафари' -> app_open (entities: ['сафари'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'закрой терминал' -> app_close (entities: ['терминал'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'open calculator' -> app_open (entities: ['calculator'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:55 |    'launch safari' -> app_open (entities: ['safari'])
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:58 | 
🔍 Test 3: Finding applications...
2025-07-12 11:05:13 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 186:193: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:66 |    ❌ Not found: Calculator
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:64 |    ✅ Found: Safari -> Safari
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:64 |    ✅ Found: Finder -> Finder
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:64 |    ✅ Found: Terminal -> Terminal
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:69 | 
🚀 Test 4: Opening Calculator...
2025-07-12 11:05:13 | INFO     | jarvis.modules.app_manager:open_application:141 | 🚀 Opening application: Calculator
2025-07-12 11:05:13 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 186:193: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:05:13 | ERROR    | __main__:test_app_manager:83 | ❌ Application 'Calculator' not found
2025-07-12 11:05:13 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:05:13 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:05:13 | INFO     | __main__:test_app_manager:89 | 
🏁 App Manager test completed!
2025-07-12 11:05:34 | INFO     | __main__:initialize:26 | Initializing Jarvis AI Assistant...
2025-07-12 11:05:34 | INFO     | jarvis.modules.voice:initialize:27 | Initializing Voice Module...
2025-07-12 11:05:34 | INFO     | jarvis.modules.voice:initialize:28 | 🔄 Using fully local speech recognition
2025-07-12 11:05:34 | INFO     | jarvis.modules.voice:initialize:35 | Calibrating microphone for ambient noise...
2025-07-12 11:05:35 | INFO     | jarvis.modules.voice:initialize:38 | Voice Module initialized successfully
2025-07-12 11:05:35 | INFO     | __main__:initialize:51 | Module VoiceModule initialized successfully
2025-07-12 11:05:35 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:05:35 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:05:35 | INFO     | __main__:initialize:51 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:05:35 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:05:35 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:05:35 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:05:35 | INFO     | __main__:initialize:51 | Module AppManager initialized successfully
2025-07-12 11:05:35 | INFO     | __main__:initialize:57 | Jarvis initialization complete!
2025-07-12 11:05:35 | INFO     | __main__:start:64 | Jarvis is now listening...
2025-07-12 11:05:35 | INFO     | __main__:start:65 | Say 'jarvis' to activate
2025-07-12 11:05:35 | INFO     | jarvis.modules.voice:start_listening:58 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:05:54 | INFO     | __main__:signal_handler:173 | Received signal 2
2025-07-12 11:05:54 | INFO     | __main__:shutdown:82 | Shutting down Jarvis...
2025-07-12 11:05:54 | INFO     | jarvis.modules.voice:stop_listening:67 | 🔇 Stopped listening
2025-07-12 11:05:54 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:05:54 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:05:54 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:05:54 | INFO     | __main__:shutdown:92 | Jarvis shutdown complete
2025-07-12 11:06:50 | INFO     | __main__:initialize:26 | Initializing Jarvis AI Assistant...
2025-07-12 11:06:50 | INFO     | jarvis.modules.voice:initialize:27 | Initializing Voice Module...
2025-07-12 11:06:50 | INFO     | jarvis.modules.voice:initialize:28 | 🔄 Using fully local speech recognition
2025-07-12 11:06:50 | INFO     | jarvis.modules.voice:initialize:35 | Calibrating microphone for ambient noise...
2025-07-12 11:06:52 | INFO     | jarvis.modules.voice:initialize:38 | Voice Module initialized successfully
2025-07-12 11:06:52 | INFO     | __main__:initialize:51 | Module VoiceModule initialized successfully
2025-07-12 11:06:52 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:06:52 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:06:52 | INFO     | __main__:initialize:51 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:06:52 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:06:52 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:06:52 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:06:52 | INFO     | __main__:initialize:51 | Module AppManager initialized successfully
2025-07-12 11:06:52 | INFO     | __main__:initialize:57 | Jarvis initialization complete!
2025-07-12 11:06:52 | INFO     | __main__:start:64 | Jarvis is now listening...
2025-07-12 11:06:52 | INFO     | __main__:start:65 | Say 'jarvis' to activate
2025-07-12 11:06:52 | INFO     | jarvis.modules.voice:start_listening:58 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:06:58 | ERROR    | jarvis.modules.voice:_listen_loop:93 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:07:04 | ERROR    | jarvis.modules.voice:_listen_loop:93 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:07:08 | ERROR    | jarvis.modules.voice:_listen_loop:93 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:07:12 | ERROR    | jarvis.modules.voice:_listen_loop:93 | Error in listen loop: There is no current event loop in thread 'Thread-1'.
2025-07-12 11:07:16 | INFO     | __main__:signal_handler:173 | Received signal 2
2025-07-12 11:07:17 | INFO     | __main__:shutdown:82 | Shutting down Jarvis...
2025-07-12 11:07:17 | INFO     | jarvis.modules.voice:stop_listening:67 | 🔇 Stopped listening
2025-07-12 11:07:17 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:07:17 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:07:17 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:07:17 | INFO     | __main__:shutdown:92 | Jarvis shutdown complete
2025-07-12 11:09:51 | INFO     | __main__:initialize:26 | Initializing Jarvis AI Assistant...
2025-07-12 11:09:51 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:09:51 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:09:51 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:09:52 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:09:52 | INFO     | __main__:initialize:51 | Module VoiceModule initialized successfully
2025-07-12 11:09:52 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:09:52 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:09:52 | INFO     | __main__:initialize:51 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:09:52 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:09:53 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:09:53 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:09:53 | INFO     | __main__:initialize:51 | Module AppManager initialized successfully
2025-07-12 11:09:53 | INFO     | __main__:initialize:57 | Jarvis initialization complete!
2025-07-12 11:09:53 | INFO     | __main__:start:64 | Jarvis is now listening...
2025-07-12 11:09:53 | INFO     | __main__:start:65 | Say 'jarvis' to activate
2025-07-12 11:09:53 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:10:24 | INFO     | __main__:signal_handler:179 | Received signal 2
2025-07-12 11:10:24 | INFO     | __main__:shutdown:88 | Shutting down Jarvis...
2025-07-12 11:10:24 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:10:24 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:10:24 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:10:24 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:10:24 | INFO     | __main__:shutdown:98 | Jarvis shutdown complete
2025-07-12 11:10:36 | INFO     | __main__:simulate_voice_commands:14 | 🚀 Starting Voice Commands Simulation...
2025-07-12 11:10:36 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:10:36 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:10:36 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:10:36 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:10:36 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:10:36 | INFO     | __main__:simulate_voice_commands:26 | ✅ Modules initialized
2025-07-12 11:10:36 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 1: 'привет' ---
2025-07-12 11:10:36 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: привет
2025-07-12 11:10:36 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'привет'
2025-07-12 11:10:36 | INFO     | __main__:process_command:52 | 🧠 Detected intent: greeting
2025-07-12 11:10:36 | INFO     | __main__:process_command:56 | 👋 Привет! Я ваш персональный помощник Jarvis!
2025-07-12 11:10:37 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 2: 'открой калькулятор' ---
2025-07-12 11:10:37 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: открой калькулятор
2025-07-12 11:10:37 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'открой калькулятор'
2025-07-12 11:10:37 | INFO     | __main__:process_command:52 | 🧠 Detected intent: app_open
2025-07-12 11:10:37 | INFO     | __main__:process_command:69 | 🚀 Opening application: Калькулятор
2025-07-12 11:10:37 | INFO     | jarvis.modules.app_manager:open_application:141 | 🚀 Opening application: Калькулятор
2025-07-12 11:10:37 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 187:194: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:10:37 | ERROR    | __main__:process_command:74 | ❌ Application 'Калькулятор' not found
2025-07-12 11:10:38 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 3: 'запусти сафари' ---
2025-07-12 11:10:38 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: запусти сафари
2025-07-12 11:10:38 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'запусти сафари'
2025-07-12 11:10:38 | INFO     | __main__:process_command:52 | 🧠 Detected intent: app_open
2025-07-12 11:10:38 | INFO     | __main__:process_command:69 | 🚀 Opening application: Safari
2025-07-12 11:10:38 | INFO     | jarvis.modules.app_manager:open_application:141 | 🚀 Opening application: Safari
2025-07-12 11:10:39 | INFO     | jarvis.modules.app_manager:open_application:158 | ✅ Successfully opened Safari
2025-07-12 11:10:39 | INFO     | __main__:process_command:72 | ✅ Successfully opened Safari
2025-07-12 11:10:40 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 4: 'помощь' ---
2025-07-12 11:10:40 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: помощь
2025-07-12 11:10:40 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'помощь'
2025-07-12 11:10:40 | INFO     | __main__:process_command:52 | 🧠 Detected intent: help
2025-07-12 11:10:40 | INFO     | __main__:process_command:62 | 💡 Доступные команды:
2025-07-12 11:10:40 | INFO     | __main__:process_command:63 |    • 'Привет' - поприветствовать
2025-07-12 11:10:40 | INFO     | __main__:process_command:64 |    • 'Открой [приложение]' - запустить приложение
2025-07-12 11:10:40 | INFO     | __main__:process_command:65 |    • 'Тест' - проверить работу
2025-07-12 11:10:41 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 5: 'тест' ---
2025-07-12 11:10:41 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: тест
2025-07-12 11:10:41 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'тест'
2025-07-12 11:10:41 | INFO     | __main__:process_command:52 | 🧠 Detected intent: test
2025-07-12 11:10:41 | INFO     | __main__:process_command:59 | ✅ Все системы работают нормально!
2025-07-12 11:10:42 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:10:42 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:10:42 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:10:42 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:10:42 | INFO     | __main__:simulate_voice_commands:107 | 
🏁 Voice Commands Simulation completed!
2025-07-12 11:11:08 | INFO     | __main__:simulate_voice_commands:14 | 🚀 Starting Voice Commands Simulation...
2025-07-12 11:11:08 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:11:08 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:11:08 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:11:08 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:11:08 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:11:08 | INFO     | __main__:simulate_voice_commands:26 | ✅ Modules initialized
2025-07-12 11:11:08 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 1: 'привет' ---
2025-07-12 11:11:08 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: привет
2025-07-12 11:11:08 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'привет'
2025-07-12 11:11:08 | INFO     | __main__:process_command:52 | 🧠 Detected intent: greeting
2025-07-12 11:11:08 | INFO     | __main__:process_command:56 | 👋 Привет! Я ваш персональный помощник Jarvis!
2025-07-12 11:11:09 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 2: 'открой калькулятор' ---
2025-07-12 11:11:09 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: открой калькулятор
2025-07-12 11:11:09 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'открой калькулятор'
2025-07-12 11:11:09 | INFO     | __main__:process_command:52 | 🧠 Detected intent: app_open
2025-07-12 11:11:09 | INFO     | __main__:process_command:69 | 🚀 Opening application: Калькулятор
2025-07-12 11:11:09 | INFO     | jarvis.modules.app_manager:open_application:155 | 🚀 Opening application: Калькулятор
2025-07-12 11:11:09 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 203:210: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:11:09 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 202:209: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:11:09 | ERROR    | __main__:process_command:74 | ❌ Application 'Калькулятор' not found
2025-07-12 11:11:10 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 3: 'запусти сафари' ---
2025-07-12 11:11:10 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: запусти сафари
2025-07-12 11:11:10 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'запусти сафари'
2025-07-12 11:11:10 | INFO     | __main__:process_command:52 | 🧠 Detected intent: app_open
2025-07-12 11:11:10 | INFO     | __main__:process_command:69 | 🚀 Opening application: Safari
2025-07-12 11:11:10 | INFO     | jarvis.modules.app_manager:open_application:155 | 🚀 Opening application: Safari
2025-07-12 11:11:11 | INFO     | jarvis.modules.app_manager:open_application:172 | ✅ Successfully opened Safari
2025-07-12 11:11:11 | INFO     | __main__:process_command:72 | ✅ Successfully opened Safari
2025-07-12 11:11:12 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 4: 'помощь' ---
2025-07-12 11:11:12 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: помощь
2025-07-12 11:11:12 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'помощь'
2025-07-12 11:11:12 | INFO     | __main__:process_command:52 | 🧠 Detected intent: help
2025-07-12 11:11:12 | INFO     | __main__:process_command:62 | 💡 Доступные команды:
2025-07-12 11:11:12 | INFO     | __main__:process_command:63 |    • 'Привет' - поприветствовать
2025-07-12 11:11:12 | INFO     | __main__:process_command:64 |    • 'Открой [приложение]' - запустить приложение
2025-07-12 11:11:12 | INFO     | __main__:process_command:65 |    • 'Тест' - проверить работу
2025-07-12 11:11:13 | INFO     | __main__:simulate_voice_commands:87 | 
--- Test 5: 'тест' ---
2025-07-12 11:11:13 | INFO     | jarvis.modules.voice:_process_voice_input_sync:119 | 📝 Command received: тест
2025-07-12 11:11:13 | INFO     | __main__:process_command:39 | 🎤 Processing voice command: 'тест'
2025-07-12 11:11:13 | INFO     | __main__:process_command:52 | 🧠 Detected intent: test
2025-07-12 11:11:13 | INFO     | __main__:process_command:59 | ✅ Все системы работают нормально!
2025-07-12 11:11:14 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:11:14 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:11:14 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:11:14 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:11:14 | INFO     | __main__:simulate_voice_commands:107 | 
🏁 Voice Commands Simulation completed!
2025-07-12 11:12:00 | INFO     | __main__:initialize:26 | Initializing Jarvis AI Assistant...
2025-07-12 11:12:00 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:12:00 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:12:00 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:12:01 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:12:01 | INFO     | __main__:initialize:51 | Module VoiceModule initialized successfully
2025-07-12 11:12:01 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:12:01 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:12:01 | INFO     | __main__:initialize:51 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:12:01 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:12:01 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:12:02 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:12:02 | INFO     | __main__:initialize:51 | Module AppManager initialized successfully
2025-07-12 11:12:02 | INFO     | __main__:initialize:57 | Jarvis initialization complete!
2025-07-12 11:12:02 | INFO     | __main__:start:64 | Jarvis is now listening...
2025-07-12 11:12:02 | INFO     | __main__:start:65 | Say 'jarvis' to activate
2025-07-12 11:12:02 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:12:23 | INFO     | __main__:signal_handler:179 | Received signal 2
2025-07-12 11:12:23 | INFO     | __main__:shutdown:88 | Shutting down Jarvis...
2025-07-12 11:12:23 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:12:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:12:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:12:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:12:23 | INFO     | __main__:shutdown:98 | Jarvis shutdown complete
2025-07-12 11:15:41 | INFO     | __main__:test_tts:12 | 🚀 Starting TTS Module test...
2025-07-12 11:15:41 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:15:41 | ERROR    | jarvis.modules.tts:_test_say_command:56 | ❌ macOS 'say' command not available
2025-07-12 11:15:41 | WARNING  | jarvis.modules.tts:initialize:28 | ⚠️ TTS may not work properly
2025-07-12 11:15:41 | ERROR    | __main__:test_tts:19 | ❌ Failed to initialize TTS Module
2025-07-12 11:16:34 | INFO     | __main__:test_tts:12 | 🚀 Starting TTS Module test...
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:16:34 | INFO     | __main__:test_tts:22 | ✅ TTS Module initialized successfully!
2025-07-12 11:16:34 | INFO     | __main__:test_tts:34 | 
--- Test 1: Speaking 'Привет! Я ваш персональный помощник Джарвис!' ---
2025-07-12 11:16:34 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Привет! Я ваш персональный помощник Джарвис!' (voice: Milena)
2025-07-12 11:16:37 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:38 | INFO     | __main__:test_tts:34 | 
--- Test 2: Speaking 'Тестирую синтез речи' ---
2025-07-12 11:16:38 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Тестирую синтез речи' (voice: Milena)
2025-07-12 11:16:39 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:40 | INFO     | __main__:test_tts:34 | 
--- Test 3: Speaking 'Все системы работают нормально' ---
2025-07-12 11:16:40 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Все системы работают нормально' (voice: Milena)
2025-07-12 11:16:42 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:43 | INFO     | __main__:test_tts:34 | 
--- Test 4: Speaking 'Открываю Сафари' ---
2025-07-12 11:16:43 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Открываю Сафари' (voice: Milena)
2025-07-12 11:16:45 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:46 | INFO     | __main__:test_tts:34 | 
--- Test 5: Speaking 'До свидания!' ---
2025-07-12 11:16:46 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'До свидания!' (voice: Milena)
2025-07-12 11:16:47 | INFO     | __main__:test_tts:38 | ✅ Speech completed successfully
2025-07-12 11:16:48 | INFO     | __main__:test_tts:46 | 
--- Testing different voices ---
2025-07-12 11:16:48 | INFO     | __main__:test_tts:52 | Testing voice: Milena
2025-07-12 11:16:48 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Тестирую разные голоса' (voice: Milena)
2025-07-12 11:16:49 | INFO     | __main__:test_tts:55 | ✅ Voice Milena works
2025-07-12 11:16:50 | INFO     | __main__:test_tts:52 | Testing voice: Yuri
2025-07-12 11:16:50 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Тестирую разные голоса' (voice: Yuri)
2025-07-12 11:16:52 | INFO     | __main__:test_tts:55 | ✅ Voice Yuri works
2025-07-12 11:16:53 | INFO     | __main__:test_tts:52 | Testing voice: Alex
2025-07-12 11:16:53 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Тестирую разные голоса' (voice: Alex)
2025-07-12 11:16:54 | INFO     | __main__:test_tts:55 | ✅ Voice Alex works
2025-07-12 11:16:55 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:16:55 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:16:55 | INFO     | __main__:test_tts:63 | 
🏁 TTS Module test completed!
2025-07-12 11:17:06 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:17:06 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:17:06 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:17:06 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:17:07 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:17:07 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:17:07 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:17:07 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:17:07 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:17:07 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:17:08 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:17:08 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:17:08 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:17:08 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:17:08 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:17:08 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:17:08 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:17:08 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:17:08 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:17:55 | INFO     | __main__:signal_handler:202 | Received signal 2
2025-07-12 11:17:56 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:17:56 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:17:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:17:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:17:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:17:56 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:17:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:17:56 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:18:49 | INFO     | __main__:debug_voice_recognition:13 | 🔍 Starting Voice Recognition Debug...
2025-07-12 11:18:49 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:18:49 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:18:50 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:18:51 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:18:51 | INFO     | __main__:debug_voice_recognition:24 | ✅ Voice module initialized
2025-07-12 11:18:51 | INFO     | __main__:debug_voice_recognition:25 | 🎤 Starting to listen...
2025-07-12 11:18:51 | INFO     | __main__:debug_voice_recognition:26 | 📢 Say 'jarvis' followed by any command
2025-07-12 11:18:51 | INFO     | __main__:debug_voice_recognition:27 | 🔍 Debug mode - will show all recognized speech
2025-07-12 11:18:51 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:19:01 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:19:01 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:19:01 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:19:11 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:19:11 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:19:11 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:19:21 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:19:21 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:19:21 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:19:31 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:19:31 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:19:31 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:19:41 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:19:41 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:19:41 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:19:51 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:19:51 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:19:51 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:19:56 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:19:56 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:19:56 | INFO     | __main__:debug_voice_recognition:69 | 🏁 Debug completed. Total commands received: 0
2025-07-12 11:20:22 | INFO     | __main__:debug_voice_recognition:13 | 🔍 Starting Voice Recognition Debug...
2025-07-12 11:20:22 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:20:22 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:20:22 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:20:23 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:20:23 | INFO     | __main__:debug_voice_recognition:24 | ✅ Voice module initialized
2025-07-12 11:20:23 | INFO     | __main__:debug_voice_recognition:25 | 🎤 Starting to listen...
2025-07-12 11:20:23 | INFO     | __main__:debug_voice_recognition:26 | 📢 Say 'jarvis' followed by any command
2025-07-12 11:20:23 | INFO     | __main__:debug_voice_recognition:27 | 🔍 Debug mode - will show all recognized speech
2025-07-12 11:20:23 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:20:23 | INFO     | jarvis.modules.voice:_listen_loop:73 | 🎧 Starting listen loop...
2025-07-12 11:20:33 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:20:33 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:20:33 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:20:42 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'jarv'
2025-07-12 11:20:43 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:20:43 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:20:43 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:20:53 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:20:53 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:20:53 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:20:54 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:21:03 | INFO     | __main__:debug_voice_recognition:58 | 📊 Status: Listening... Commands received: 0
2025-07-12 11:21:03 | INFO     | __main__:debug_voice_recognition:59 | 🔊 Wake word detected: False
2025-07-12 11:21:03 | INFO     | __main__:debug_voice_recognition:60 | 🎤 Is listening: True
2025-07-12 11:21:12 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:21:12 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:21:12 | INFO     | __main__:debug_voice_recognition:69 | 🏁 Debug completed. Total commands received: 0
2025-07-12 11:22:26 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:22:26 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:22:26 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:22:26 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:22:27 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:22:27 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:22:27 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:22:27 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:22:27 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:22:27 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:22:28 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:22:28 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:22:28 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:22:28 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:22:28 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:22:28 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:22:28 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:22:28 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:22:28 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:22:28 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:22:28 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:22:28 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:22:28 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:22:28 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:22:28 | INFO     | jarvis.modules.voice:_listen_loop:73 | 🎧 Starting listen loop...
2025-07-12 11:23:16 | INFO     | __main__:signal_handler:202 | Received signal 2
2025-07-12 11:23:16 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:23:16 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:23:16 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:23:16 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:23:16 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:23:16 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:23:16 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:23:16 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:23:30 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:23:30 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:23:30 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:23:30 | INFO     | jarvis.modules.voice:initialize:37 | Calibrating microphone for ambient noise...
2025-07-12 11:23:31 | INFO     | jarvis.modules.voice:initialize:40 | Voice Module initialized successfully
2025-07-12 11:23:31 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:23:31 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:23:31 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:23:31 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:23:31 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:23:31 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:23:31 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:23:31 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:23:31 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:23:31 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:23:32 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:23:32 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:23:32 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:23:32 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:23:32 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:23:32 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:23:32 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:23:32 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:23:32 | INFO     | jarvis.modules.voice:start_listening:60 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:23:32 | INFO     | jarvis.modules.voice:_listen_loop:73 | 🎧 Starting listen loop...
2025-07-12 11:23:35 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'Джарвис Привет'
2025-07-12 11:23:35 | INFO     | jarvis.modules.voice:_process_voice_input_sync:126 | 🎯 Wake word detected in 'джарвис привет'! Listening for command...
2025-07-12 11:23:54 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'сервис т'
2025-07-12 11:23:54 | INFO     | jarvis.modules.voice:_process_voice_input_sync:130 | 📝 Command received: сервис т
2025-07-12 11:23:54 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'сервис т'
2025-07-12 11:23:54 | INFO     | __main__:_handle_voice_command:116 | 🧠 Detected intent: unknown
2025-07-12 11:23:54 | INFO     | __main__:_handle_voice_command:175 | 🤔 Команда 'сервис т' не распознана
2025-07-12 11:23:54 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Извините, я не понял команду. Скажите помощь для списка команд.' (voice: Milena)
2025-07-12 11:23:56 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'Извините я'
2025-07-12 11:23:58 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'Скажите помощь для спи'
2025-07-12 11:24:08 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'помощь'
2025-07-12 11:24:20 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'Джа'
2025-07-12 11:24:24 | INFO     | jarvis.modules.voice:_recognize_speech:107 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:24:24 | INFO     | jarvis.modules.voice:_process_voice_input_sync:126 | 🎯 Wake word detected in 'джарвис'! Listening for command...
2025-07-12 11:25:23 | INFO     | __main__:signal_handler:202 | Received signal 2
2025-07-12 11:25:23 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:25:23 | INFO     | jarvis.modules.voice:stop_listening:69 | 🔇 Stopped listening
2025-07-12 11:25:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:25:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:25:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:25:23 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:25:23 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:25:23 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:28:11 | INFO     | __main__:test_continuous_listening:13 | 🚀 Starting Continuous Listening Test...
2025-07-12 11:28:11 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:28:11 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:28:11 | INFO     | jarvis.modules.voice:initialize:42 | Calibrating microphone for ambient noise...
2025-07-12 11:28:13 | INFO     | jarvis.modules.voice:initialize:44 | Energy threshold set to: 24.95942409618629
2025-07-12 11:28:13 | INFO     | jarvis.modules.voice:initialize:46 | Voice Module initialized successfully
2025-07-12 11:28:13 | INFO     | __main__:test_continuous_listening:24 | ✅ Voice module initialized
2025-07-12 11:28:13 | INFO     | __main__:test_continuous_listening:25 | 🎤 Starting continuous listening...
2025-07-12 11:28:13 | INFO     | __main__:test_continuous_listening:26 | 📢 Try saying:
2025-07-12 11:28:13 | INFO     | __main__:test_continuous_listening:27 |    • 'Джарвис' (to activate)
2025-07-12 11:28:13 | INFO     | __main__:test_continuous_listening:28 |    • Then any command like 'привет', 'тест', 'открой сафари'
2025-07-12 11:28:13 | INFO     | __main__:test_continuous_listening:29 | 🔍 Monitoring for 60 seconds...
2025-07-12 11:28:13 | INFO     | jarvis.modules.voice:start_listening:66 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:28:13 | INFO     | jarvis.modules.voice:_listen_loop:79 | 🎧 Starting continuous listen loop...
2025-07-12 11:28:13 | INFO     | jarvis.modules.voice:_listen_loop:83 | 🎤 Adjusting for ambient noise...
2025-07-12 11:28:23 | INFO     | __main__:test_continuous_listening:64 | 📊 Status: 10s elapsed, 50s remaining. Commands: 0
2025-07-12 11:28:23 | INFO     | __main__:test_continuous_listening:65 | 🔊 Wake word state: False
2025-07-12 11:28:33 | INFO     | __main__:test_continuous_listening:64 | 📊 Status: 20s elapsed, 40s remaining. Commands: 0
2025-07-12 11:28:33 | INFO     | __main__:test_continuous_listening:65 | 🔊 Wake word state: False
2025-07-12 11:28:43 | INFO     | __main__:test_continuous_listening:64 | 📊 Status: 30s elapsed, 30s remaining. Commands: 0
2025-07-12 11:28:43 | INFO     | __main__:test_continuous_listening:65 | 🔊 Wake word state: False
2025-07-12 11:28:53 | INFO     | __main__:test_continuous_listening:64 | 📊 Status: 40s elapsed, 20s remaining. Commands: 0
2025-07-12 11:28:53 | INFO     | __main__:test_continuous_listening:65 | 🔊 Wake word state: False
2025-07-12 11:28:54 | INFO     | jarvis.modules.voice:stop_listening:75 | 🔇 Stopped listening
2025-07-12 11:28:54 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:28:54 | INFO     | __main__:test_continuous_listening:74 | 🏁 Test completed. Total commands received: 0
2025-07-12 11:28:54 | WARNING  | __main__:test_continuous_listening:79 | ⚠️ No commands received. Check microphone and try speaking louder.
2025-07-12 11:29:05 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:29:05 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:29:05 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:29:05 | INFO     | jarvis.modules.voice:initialize:42 | Calibrating microphone for ambient noise...
2025-07-12 11:29:07 | INFO     | jarvis.modules.voice:initialize:44 | Energy threshold set to: 18.230045669983728
2025-07-12 11:29:07 | INFO     | jarvis.modules.voice:initialize:46 | Voice Module initialized successfully
2025-07-12 11:29:07 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:29:07 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:29:07 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:29:07 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:29:07 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:29:07 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:29:07 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:29:07 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:29:07 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:29:08 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:29:08 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:29:08 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:29:08 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:29:08 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:29:08 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:29:08 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:29:08 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:29:08 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:29:08 | INFO     | jarvis.modules.voice:start_listening:66 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:29:08 | INFO     | jarvis.modules.voice:_listen_loop:79 | 🎧 Starting continuous listen loop...
2025-07-12 11:29:08 | INFO     | jarvis.modules.voice:_listen_loop:83 | 🎤 Adjusting for ambient noise...
2025-07-12 11:29:26 | INFO     | __main__:signal_handler:202 | Received signal 2
2025-07-12 11:29:26 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:29:26 | INFO     | jarvis.modules.voice:stop_listening:75 | 🔇 Stopped listening
2025-07-12 11:29:26 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:29:26 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:29:26 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:29:26 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:29:26 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:29:26 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:29:40 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:29:40 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:29:40 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:29:40 | INFO     | jarvis.modules.voice:initialize:42 | Calibrating microphone for ambient noise...
2025-07-12 11:29:42 | INFO     | jarvis.modules.voice:initialize:44 | Energy threshold set to: 38.172494855821355
2025-07-12 11:29:42 | INFO     | jarvis.modules.voice:initialize:46 | Voice Module initialized successfully
2025-07-12 11:29:42 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:29:42 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:29:42 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:29:42 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:29:42 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:29:42 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:29:42 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:29:42 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:29:42 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:29:42 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:29:43 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:29:43 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:29:43 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:29:43 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:29:43 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:29:43 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:29:43 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:29:43 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:29:43 | INFO     | jarvis.modules.voice:start_listening:66 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:29:43 | INFO     | jarvis.modules.voice:_listen_loop:79 | 🎧 Starting continuous listen loop...
2025-07-12 11:29:43 | INFO     | jarvis.modules.voice:_listen_loop:83 | 🎤 Adjusting for ambient noise...
2025-07-12 11:29:55 | INFO     | jarvis.modules.voice:_recognize_speech:122 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:29:55 | INFO     | jarvis.modules.voice:_process_voice_input_sync:141 | 🎯 Wake word detected in 'джарвис'! Listening for command...
2025-07-12 11:30:02 | INFO     | jarvis.modules.voice:_recognize_speech:122 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:30:02 | INFO     | jarvis.modules.voice:_process_voice_input_sync:145 | 📝 Command received: джарвис
2025-07-12 11:30:02 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'джарвис'
2025-07-12 11:30:02 | INFO     | __main__:_handle_voice_command:116 | 🧠 Detected intent: unknown
2025-07-12 11:30:02 | INFO     | __main__:_handle_voice_command:175 | 🤔 Команда 'джарвис' не распознана
2025-07-12 11:30:02 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Извините, я не понял команду. Скажите помощь для списка команд.' (voice: Milena)
2025-07-12 11:30:04 | INFO     | jarvis.modules.voice:_recognize_speech:122 | 🎯 Recognized speech: 'Извините я'
2025-07-12 11:30:06 | INFO     | jarvis.modules.voice:_recognize_speech:122 | 🎯 Recognized speech: 'для списка'
2025-07-12 11:30:08 | INFO     | jarvis.modules.voice:_recognize_speech:122 | 🎯 Recognized speech: 'сервис'
2025-07-12 11:30:17 | INFO     | jarvis.modules.voice:_recognize_speech:122 | 🎯 Recognized speech: 'jarv'
2025-07-12 11:30:17 | INFO     | jarvis.modules.voice:_process_voice_input_sync:141 | 🎯 Wake word detected in 'jarv'! Listening for command...
2025-07-12 11:30:51 | INFO     | __main__:signal_handler:202 | Received signal 2
2025-07-12 11:30:51 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:30:51 | INFO     | jarvis.modules.voice:stop_listening:75 | 🔇 Stopped listening
2025-07-12 11:30:51 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:30:51 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:30:51 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:30:51 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:30:51 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:30:51 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:32:17 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:32:17 | INFO     | jarvis.modules.voice:initialize:29 | Initializing Voice Module...
2025-07-12 11:32:17 | INFO     | jarvis.modules.voice:initialize:30 | 🔄 Using fully local speech recognition
2025-07-12 11:32:17 | INFO     | jarvis.modules.voice:initialize:42 | Calibrating microphone for ambient noise...
2025-07-12 11:32:19 | INFO     | jarvis.modules.voice:initialize:44 | Energy threshold set to: 47.76170428085147
2025-07-12 11:32:19 | INFO     | jarvis.modules.voice:initialize:46 | Voice Module initialized successfully
2025-07-12 11:32:19 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:32:19 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:32:19 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:32:19 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:32:19 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:32:20 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:32:20 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:32:20 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:32:20 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:32:20 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:32:20 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:32:20 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:32:20 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:32:20 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:32:20 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:32:20 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:32:20 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:32:20 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:32:20 | INFO     | jarvis.modules.voice:start_listening:66 | 🎤 Started listening for wake word: 'jarvis'
2025-07-12 11:32:20 | INFO     | jarvis.modules.voice:_listen_loop:79 | 🎧 Starting continuous listen loop...
2025-07-12 11:32:20 | INFO     | jarvis.modules.voice:_listen_loop:83 | 🎤 Adjusting for ambient noise...
2025-07-12 11:32:24 | INFO     | __main__:signal_handler:202 | Received signal 2
2025-07-12 11:32:25 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:32:25 | INFO     | jarvis.modules.voice:stop_listening:75 | 🔇 Stopped listening
2025-07-12 11:32:25 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:32:25 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:32:25 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:32:25 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:32:25 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:32:25 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:35:17 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:35:17 | INFO     | jarvis.modules.voice:initialize:38 | Initializing Voice Module...
2025-07-12 11:35:17 | INFO     | jarvis.modules.voice:initialize:39 | 🔄 Using fully local speech recognition
2025-07-12 11:35:18 | INFO     | jarvis.modules.voice:initialize:53 | Calibrating microphone for ambient noise...
2025-07-12 11:35:20 | INFO     | jarvis.modules.voice:initialize:55 | Energy threshold set to: 32.516178995927426
2025-07-12 11:35:20 | INFO     | jarvis.modules.voice:initialize:57 | Voice Module initialized successfully
2025-07-12 11:35:20 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:35:20 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:35:20 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:35:20 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:35:20 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:35:20 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:35:20 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:35:20 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:35:20 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:35:20 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:35:20 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:35:20 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:35:20 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:35:20 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:35:20 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:35:20 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:35:20 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:35:20 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:35:20 | INFO     | jarvis.modules.voice:start_listening:79 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 11:35:20 | INFO     | jarvis.modules.voice:_continuous_listen_loop:98 | 🎧 Starting optimized continuous listen loop...
2025-07-12 11:35:20 | INFO     | jarvis.modules.voice:_continuous_listen_loop:103 | 🎤 Microphone opened for continuous listening
2025-07-12 11:35:53 | INFO     | __main__:signal_handler:202 | Received signal 2
2025-07-12 11:35:53 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:35:53 | INFO     | jarvis.modules.voice:stop_listening:90 | 🔇 Stopped listening
2025-07-12 11:35:53 | INFO     | jarvis.modules.voice:_continuous_listen_loop:140 | 🎧 Continuous listen loop ended
2025-07-12 11:35:53 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:35:53 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:35:53 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:35:53 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:35:53 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:35:53 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:36:27 | INFO     | __main__:test_voice_module:21 | 🧪 Starting Voice Module Test
2025-07-12 11:36:27 | INFO     | jarvis.modules.voice:initialize:38 | Initializing Voice Module...
2025-07-12 11:36:27 | INFO     | jarvis.modules.voice:initialize:39 | 🔄 Using fully local speech recognition
2025-07-12 11:36:28 | INFO     | jarvis.modules.voice:initialize:53 | Calibrating microphone for ambient noise...
2025-07-12 11:36:30 | INFO     | jarvis.modules.voice:initialize:55 | Energy threshold set to: 37.20387132641055
2025-07-12 11:36:30 | INFO     | jarvis.modules.voice:initialize:57 | Voice Module initialized successfully
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:29 | ✅ Voice module initialized successfully
2025-07-12 11:36:30 | INFO     | jarvis.modules.voice:start_listening:79 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 11:36:30 | INFO     | jarvis.modules.voice:_continuous_listen_loop:98 | 🎧 Starting optimized continuous listen loop...
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:33 | 🎤 Voice module is listening...
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:39 | 🕐 Testing for 30 seconds...
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:40 | 💬 Try saying:
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:41 |    • 'Джарвис привет'
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:42 |    • 'Jarvis открой сафари'
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:43 |    • 'Джарвис тест'
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:44 |    • 'Jarvis помощь'
2025-07-12 11:36:30 | INFO     | __main__:test_voice_module:53 | 🔄 State changed:  → listening
2025-07-12 11:36:30 | INFO     | jarvis.modules.voice:_continuous_listen_loop:103 | 🎤 Microphone opened for continuous listening
2025-07-12 11:36:32 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:32 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:33 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:33 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:38 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:38 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:43 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:44 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:46 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:46 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:47 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:47 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:48 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:49 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'Привет'
2025-07-12 11:36:49 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:52 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:52 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:54 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:54 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:55 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:56 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:57 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:58 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:36:59 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: listening → processing
2025-07-12 11:36:59 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:36:59 | INFO     | jarvis.modules.voice:_process_voice_input:186 | 🎯 Wake word detected in: 'джарвис'
2025-07-12 11:36:59 | INFO     | jarvis.modules.voice:_process_voice_input:199 | ⏳ Waiting for command in next phrase...
2025-07-12 11:36:59 | INFO     | __main__:test_voice_module:53 | 🔄 State changed: processing → listening
2025-07-12 11:37:00 | INFO     | jarvis.modules.voice:stop_listening:90 | 🔇 Stopped listening
2025-07-12 11:37:00 | INFO     | jarvis.modules.voice:_continuous_listen_loop:140 | 🎧 Continuous listen loop ended
2025-07-12 11:37:00 | INFO     | jarvis.modules.voice:stop_listening:90 | 🔇 Stopped listening
2025-07-12 11:37:00 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:37:00 | INFO     | __main__:test_voice_module:73 | 🏁 Test completed!
2025-07-12 11:37:00 | INFO     | __main__:test_voice_module:74 | 📊 Commands received: 0
2025-07-12 11:37:00 | WARNING  | __main__:test_voice_module:81 | ⚠️  No commands were received during test
2025-07-12 11:37:00 | ERROR    | __main__:main:152 | ❌ Voice module test FAILED!
2025-07-12 11:37:00 | INFO     | jarvis.modules.voice:stop_listening:90 | 🔇 Stopped listening
2025-07-12 11:37:00 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:38:38 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:38:38 | INFO     | jarvis.modules.voice:initialize:38 | Initializing Voice Module...
2025-07-12 11:38:38 | INFO     | jarvis.modules.voice:initialize:39 | 🔄 Using fully local speech recognition
2025-07-12 11:38:38 | INFO     | jarvis.modules.voice:initialize:53 | Calibrating microphone for ambient noise...
2025-07-12 11:38:40 | INFO     | jarvis.modules.voice:initialize:55 | Energy threshold set to: 44.693300921583514
2025-07-12 11:38:40 | INFO     | jarvis.modules.voice:initialize:57 | Voice Module initialized successfully
2025-07-12 11:38:40 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:38:40 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:38:40 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:38:40 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:38:40 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:38:40 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:38:40 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:38:40 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:38:40 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:38:40 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:38:41 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:38:41 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:38:41 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:38:41 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:38:41 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:38:41 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:38:41 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:38:41 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:38:41 | INFO     | jarvis.modules.voice:start_listening:79 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 11:38:41 | INFO     | jarvis.modules.voice:_continuous_listen_loop:98 | 🎧 Starting optimized continuous listen loop...
2025-07-12 11:38:41 | INFO     | jarvis.modules.voice:_continuous_listen_loop:103 | 🎤 Microphone opened for continuous listening
2025-07-12 11:38:52 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:38:52 | INFO     | jarvis.modules.voice:_process_voice_input:186 | 🎯 Wake word detected in: 'джарвис'
2025-07-12 11:38:52 | INFO     | jarvis.modules.voice:_process_voice_input:199 | 👂 Wake word only - responding and waiting for command...
2025-07-12 11:38:52 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: '__WAKE_WORD_ACTIVATED__'
2025-07-12 11:38:52 | INFO     | __main__:_handle_voice_command:109 | 👂 Да, слушаю вас!
2025-07-12 11:38:52 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Да, слушаю вас!' (voice: Milena)
2025-07-12 11:38:55 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'Да слуша'
2025-07-12 11:38:55 | INFO     | jarvis.modules.voice:_process_voice_input:211 | 📝 Command received after wake word: 'да слуша'
2025-07-12 11:38:55 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'да слуша'
2025-07-12 11:38:55 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: unknown
2025-07-12 11:38:55 | INFO     | __main__:_handle_voice_command:182 | 🤔 Команда 'да слуша' не распознана
2025-07-12 11:38:55 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Извините, я не понял команду. Скажите помощь для списка команд.' (voice: Milena)
2025-07-12 11:38:56 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'Извините я'
2025-07-12 11:38:59 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'списка'
2025-07-12 11:39:08 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'помощь'
2025-07-12 11:39:13 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'Джа'
2025-07-12 11:39:18 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'джар в помощь'
2025-07-12 11:39:24 | INFO     | jarvis.modules.voice:_recognize_speech:161 | 🎯 Recognized speech: 'джар'
2025-07-12 11:39:24 | INFO     | __main__:signal_handler:209 | Received signal 2
2025-07-12 11:39:24 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:39:24 | INFO     | jarvis.modules.voice:stop_listening:90 | 🔇 Stopped listening
2025-07-12 11:39:25 | INFO     | jarvis.modules.voice:_continuous_listen_loop:140 | 🎧 Continuous listen loop ended
2025-07-12 11:39:25 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:39:25 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:39:25 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:39:25 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:39:25 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:39:25 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:41:40 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:41:40 | INFO     | jarvis.modules.voice:initialize:39 | Initializing Voice Module...
2025-07-12 11:41:40 | INFO     | jarvis.modules.voice:initialize:40 | 🔄 Using fully local speech recognition
2025-07-12 11:41:40 | INFO     | jarvis.modules.voice:initialize:54 | Calibrating microphone for ambient noise...
2025-07-12 11:41:42 | INFO     | jarvis.modules.voice:initialize:56 | Energy threshold set to: 49.04402050621256
2025-07-12 11:41:42 | INFO     | jarvis.modules.voice:initialize:58 | Voice Module initialized successfully
2025-07-12 11:41:42 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:41:42 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:41:42 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:41:42 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:41:42 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:41:42 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:41:42 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:41:42 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:41:42 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:41:43 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:41:43 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:41:43 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:41:43 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:41:43 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:41:43 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:41:43 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:41:43 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:41:43 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:41:43 | INFO     | jarvis.modules.voice:start_listening:80 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 11:41:43 | INFO     | jarvis.modules.voice:_continuous_listen_loop:99 | 🎧 Starting optimized continuous listen loop...
2025-07-12 11:41:43 | INFO     | jarvis.modules.voice:_continuous_listen_loop:104 | 🎤 Microphone opened for continuous listening
2025-07-12 11:41:48 | INFO     | jarvis.modules.voice:_recognize_speech:174 | 🎯 Recognized speech: 'Привет'
2025-07-12 11:41:53 | INFO     | jarvis.modules.voice:_recognize_speech:174 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:41:53 | INFO     | jarvis.modules.voice:_process_voice_input:199 | 🎯 Wake word detected in: 'джарвис'
2025-07-12 11:41:53 | INFO     | jarvis.modules.voice:_process_voice_input:212 | 👂 Wake word only - responding and waiting for command...
2025-07-12 11:41:53 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: '__WAKE_WORD_ACTIVATED__'
2025-07-12 11:41:53 | INFO     | __main__:_handle_voice_command:109 | 👂 Да, слушаю вас!
2025-07-12 11:41:53 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Да, слушаю вас!' (voice: Milena)
2025-07-12 11:41:59 | INFO     | jarvis.modules.voice:_recognize_speech:174 | 🎯 Recognized speech: 'Привет'
2025-07-12 11:41:59 | INFO     | jarvis.modules.voice:_process_voice_input:224 | 📝 Command received after wake word: 'привет'
2025-07-12 11:41:59 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'привет'
2025-07-12 11:41:59 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: greeting
2025-07-12 11:41:59 | INFO     | __main__:_handle_voice_command:128 | 👋 Привет! Я ваш персональный помощник Джарвис!
2025-07-12 11:41:59 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Привет! Я ваш персональный помощник Джарвис!' (voice: Milena)
2025-07-12 11:42:05 | INFO     | jarvis.modules.voice:_recognize_speech:174 | 🎯 Recognized speech: 'открой'
2025-07-12 11:42:11 | INFO     | jarvis.modules.voice:_recognize_speech:174 | 🎯 Recognized speech: 'открой'
2025-07-12 11:42:23 | INFO     | jarvis.modules.voice:_recognize_speech:174 | 🎯 Recognized speech: 'открой'
2025-07-12 11:42:26 | INFO     | jarvis.modules.voice:_recognize_speech:174 | 🎯 Recognized speech: 'Сафари'
2025-07-12 11:42:32 | INFO     | jarvis.modules.voice:_recognize_speech:174 | 🎯 Recognized speech: 'Открой са'
2025-07-12 11:42:36 | INFO     | __main__:signal_handler:227 | Received signal 2
2025-07-12 11:42:36 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:42:36 | INFO     | jarvis.modules.voice:stop_listening:91 | 🔇 Stopped listening
2025-07-12 11:42:37 | INFO     | jarvis.modules.voice:_continuous_listen_loop:152 | 🎧 Continuous listen loop ended
2025-07-12 11:42:37 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:42:37 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:42:37 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:42:37 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:42:37 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:42:37 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:44:46 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:44:46 | INFO     | jarvis.modules.voice:initialize:44 | Initializing Voice Module...
2025-07-12 11:44:46 | INFO     | jarvis.modules.voice:initialize:45 | 🔄 Using fully local speech recognition
2025-07-12 11:44:47 | INFO     | jarvis.modules.voice:initialize:59 | Calibrating microphone for ambient noise...
2025-07-12 11:44:49 | INFO     | jarvis.modules.voice:initialize:61 | Energy threshold set to: 50.83819684368548
2025-07-12 11:44:49 | INFO     | jarvis.modules.voice:initialize:63 | Voice Module initialized successfully
2025-07-12 11:44:49 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:44:49 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:44:49 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:44:49 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:44:49 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:44:49 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:44:49 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:44:49 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:44:49 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:44:49 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:44:49 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:44:49 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:44:49 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:44:49 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:44:49 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:44:49 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:44:49 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:44:49 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:44:49 | INFO     | jarvis.modules.voice:start_listening:85 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 11:44:49 | INFO     | jarvis.modules.voice:_continuous_listen_loop:104 | 🎧 Starting optimized continuous listen loop...
2025-07-12 11:44:50 | INFO     | jarvis.modules.voice:_continuous_listen_loop:109 | 🎤 Microphone opened for continuous listening
2025-07-12 11:44:55 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:44:57 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис' (from 1 fragments)
2025-07-12 11:44:57 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис'
2025-07-12 11:44:57 | INFO     | jarvis.modules.voice:_process_voice_input:246 | 👂 Wake word only - responding and waiting for command...
2025-07-12 11:44:57 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: '__WAKE_WORD_ACTIVATED__'
2025-07-12 11:44:57 | INFO     | __main__:_handle_voice_command:109 | 👂 Да, слушаю вас!
2025-07-12 11:44:57 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Да, слушаю вас!' (voice: Milena)
2025-07-12 11:45:02 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Да слушаю вас Привет'
2025-07-12 11:45:04 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'да слушаю вас привет' (from 1 fragments)
2025-07-12 11:45:04 | INFO     | jarvis.modules.voice:_process_voice_input:258 | 📝 Command received after wake word: 'да слушаю вас привет'
2025-07-12 11:45:04 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'да слушаю вас привет'
2025-07-12 11:45:04 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: greeting
2025-07-12 11:45:04 | INFO     | __main__:_handle_voice_command:128 | 👋 Привет! Я ваш персональный помощник Джарвис!
2025-07-12 11:45:04 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Привет! Я ваш персональный помощник Джарвис!' (voice: Milena)
2025-07-12 11:45:12 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Привет Я ваш персональный помощник Джарвис открой Сафари'
2025-07-12 11:45:14 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет я ваш персональный помощник джарвис открой сафари' (from 1 fragments)
2025-07-12 11:45:14 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'привет я ваш персональный помощник джарвис открой сафари'
2025-07-12 11:45:14 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'открой сафари'
2025-07-12 11:45:14 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'открой сафари'
2025-07-12 11:45:14 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: app_open
2025-07-12 11:45:14 | INFO     | __main__:_handle_voice_command:156 | 🚀 Opening application: Safari
2025-07-12 11:45:14 | INFO     | jarvis.modules.app_manager:open_application:156 | 🚀 Opening application: Safari
2025-07-12 11:45:14 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 198:205: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:45:15 | INFO     | jarvis.modules.app_manager:open_application:181 | ✅ Successfully opened Safari
2025-07-12 11:45:15 | INFO     | __main__:_handle_voice_command:160 | ✅ Successfully opened Safari
2025-07-12 11:45:15 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Открываю Safari' (voice: Milena)
2025-07-12 11:45:19 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'открываю Сафари'
2025-07-12 11:45:21 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открываю сафари' (from 1 fragments)
2025-07-12 11:45:22 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Спасибо'
2025-07-12 11:45:24 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'спасибо' (from 1 fragments)
2025-07-12 11:45:36 | INFO     | __main__:signal_handler:227 | Received signal 2
2025-07-12 11:45:36 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:45:36 | INFO     | jarvis.modules.voice:stop_listening:96 | 🔇 Stopped listening
2025-07-12 11:45:38 | INFO     | jarvis.modules.voice:_continuous_listen_loop:157 | 🎧 Continuous listen loop ended
2025-07-12 11:45:38 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:45:38 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:45:38 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:45:38 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:45:38 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:45:38 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:48:15 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:48:15 | INFO     | jarvis.modules.voice:initialize:44 | Initializing Voice Module...
2025-07-12 11:48:15 | INFO     | jarvis.modules.voice:initialize:45 | 🔄 Using fully local speech recognition
2025-07-12 11:48:15 | INFO     | jarvis.modules.voice:initialize:59 | Calibrating microphone for ambient noise...
2025-07-12 11:48:17 | INFO     | jarvis.modules.voice:initialize:61 | Energy threshold set to: 34.27518328027262
2025-07-12 11:48:17 | INFO     | jarvis.modules.voice:initialize:63 | Voice Module initialized successfully
2025-07-12 11:48:17 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:48:17 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:48:17 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:48:17 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:48:17 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:48:17 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:48:18 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:48:18 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:48:18 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:48:18 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:48:18 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:48:18 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:48:18 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:48:18 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:48:18 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:48:18 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:48:18 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:48:18 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:48:18 | INFO     | jarvis.modules.voice:start_listening:85 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 11:48:18 | INFO     | jarvis.modules.voice:_continuous_listen_loop:104 | 🎧 Starting optimized continuous listen loop...
2025-07-12 11:48:18 | INFO     | jarvis.modules.voice:_continuous_listen_loop:109 | 🎤 Microphone opened for continuous listening
2025-07-12 11:48:22 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:48:24 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис' (from 1 fragments)
2025-07-12 11:48:24 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис'
2025-07-12 11:48:24 | INFO     | jarvis.modules.voice:_process_voice_input:246 | 👂 Wake word only - responding and waiting for command...
2025-07-12 11:48:24 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: '__WAKE_WORD_ACTIVATED__'
2025-07-12 11:48:24 | INFO     | __main__:_handle_voice_command:109 | 👂 Да, слушаю вас!
2025-07-12 11:48:24 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Да, слушаю вас!' (voice: Milena)
2025-07-12 11:48:29 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Привет Да слушаю вас Привет'
2025-07-12 11:48:31 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет да слушаю вас привет' (from 1 fragments)
2025-07-12 11:48:31 | INFO     | jarvis.modules.voice:_process_voice_input:258 | 📝 Command received after wake word: 'привет да слушаю вас привет'
2025-07-12 11:48:31 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'привет да слушаю вас привет'
2025-07-12 11:48:31 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: greeting
2025-07-12 11:48:31 | INFO     | __main__:_handle_voice_command:128 | 👋 Привет! Я ваш персональный помощник Джарвис!
2025-07-12 11:48:31 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Привет! Я ваш персональный помощник Джарвис!' (voice: Milena)
2025-07-12 11:48:36 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Привет Я ваш персональный помощник Джарвис'
2025-07-12 11:48:38 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет я ваш персональный помощник джарвис' (from 1 fragments)
2025-07-12 11:48:38 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'привет я ваш персональный помощник джарвис'
2025-07-12 11:48:38 | INFO     | jarvis.modules.voice:_process_voice_input:246 | 👂 Wake word only - responding and waiting for command...
2025-07-12 11:48:38 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: '__WAKE_WORD_ACTIVATED__'
2025-07-12 11:48:38 | INFO     | __main__:_handle_voice_command:109 | 👂 Да, слушаю вас!
2025-07-12 11:48:38 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Да, слушаю вас!' (voice: Milena)
2025-07-12 11:48:44 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Открой Сафари'
2025-07-12 11:48:46 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открой сафари' (from 1 fragments)
2025-07-12 11:48:46 | INFO     | jarvis.modules.voice:_process_voice_input:258 | 📝 Command received after wake word: 'открой сафари'
2025-07-12 11:48:46 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'открой сафари'
2025-07-12 11:48:46 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: app_open
2025-07-12 11:48:46 | INFO     | __main__:_handle_voice_command:156 | 🚀 Opening application: Safari
2025-07-12 11:48:46 | INFO     | jarvis.modules.app_manager:open_application:156 | 🚀 Opening application: Safari
2025-07-12 11:48:46 | INFO     | jarvis.modules.app_manager:open_application:181 | ✅ Successfully opened Safari
2025-07-12 11:48:46 | INFO     | __main__:_handle_voice_command:160 | ✅ Successfully opened Safari
2025-07-12 11:48:46 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Открываю Safari' (voice: Milena)
2025-07-12 11:48:50 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'открываем Сафари'
2025-07-12 11:48:52 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открываем сафари' (from 1 fragments)
2025-07-12 11:48:52 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'закрой Сафари'
2025-07-12 11:48:54 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'закрой сафари' (from 1 fragments)
2025-07-12 11:48:59 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'закрой Сафари'
2025-07-12 11:49:01 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'закрой сафари' (from 1 fragments)
2025-07-12 11:49:10 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Ну блин она не знает закрыть'
2025-07-12 11:49:12 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'ну блин она не знает закрыть' (from 1 fragments)
2025-07-12 11:49:26 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Как дела'
2025-07-12 11:49:28 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'как дела' (from 1 fragments)
2025-07-12 11:49:41 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис закрой Сафари'
2025-07-12 11:49:43 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис закрой сафари' (from 1 fragments)
2025-07-12 11:49:43 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис закрой сафари'
2025-07-12 11:49:43 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'закрой сафари'
2025-07-12 11:49:43 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'закрой сафари'
2025-07-12 11:49:43 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: app_close
2025-07-12 11:49:43 | INFO     | __main__:_handle_voice_command:169 | 🔴 Closing application: Safari
2025-07-12 11:49:43 | INFO     | jarvis.modules.app_manager:close_application:201 | 🔴 Closing application: Safari
2025-07-12 11:49:43 | INFO     | jarvis.modules.app_manager:close_application:218 | ✅ Successfully closed Safari
2025-07-12 11:49:43 | INFO     | __main__:_handle_voice_command:173 | ✅ Successfully closed Safari
2025-07-12 11:49:43 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Закрываю Safari' (voice: Milena)
2025-07-12 11:50:32 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:50:34 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис' (from 1 fragments)
2025-07-12 11:50:34 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис'
2025-07-12 11:50:34 | INFO     | jarvis.modules.voice:_process_voice_input:246 | 👂 Wake word only - responding and waiting for command...
2025-07-12 11:50:34 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: '__WAKE_WORD_ACTIVATED__'
2025-07-12 11:50:34 | INFO     | __main__:_handle_voice_command:109 | 👂 Да, слушаю вас!
2025-07-12 11:50:34 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Да, слушаю вас!' (voice: Milena)
2025-07-12 11:50:39 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'привет да привет'
2025-07-12 11:50:41 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет да привет' (from 1 fragments)
2025-07-12 11:50:41 | INFO     | jarvis.modules.voice:_process_voice_input:258 | 📝 Command received after wake word: 'привет да привет'
2025-07-12 11:50:41 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'привет да привет'
2025-07-12 11:50:41 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: greeting
2025-07-12 11:50:41 | INFO     | __main__:_handle_voice_command:128 | 👋 Привет! Я ваш персональный помощник Джарвис!
2025-07-12 11:50:41 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Привет! Я ваш персональный помощник Джарвис!' (voice: Milena)
2025-07-12 11:50:49 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Привет Я ваш персональный помощник Джарвис OK Google открой Сафари'
2025-07-12 11:50:51 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет я ваш персональный помощник джарвис ok google открой сафари' (from 1 fragments)
2025-07-12 11:50:51 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'привет я ваш персональный помощник джарвис ok google открой сафари'
2025-07-12 11:50:51 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'ok google открой сафари'
2025-07-12 11:50:51 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'ok google открой сафари'
2025-07-12 11:50:51 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: app_open
2025-07-12 11:50:51 | INFO     | __main__:_handle_voice_command:156 | 🚀 Opening application: Safari
2025-07-12 11:50:51 | INFO     | jarvis.modules.app_manager:open_application:156 | 🚀 Opening application: Safari
2025-07-12 11:50:51 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 198:205: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:50:51 | INFO     | jarvis.modules.app_manager:open_application:181 | ✅ Successfully opened Safari
2025-07-12 11:50:51 | INFO     | __main__:_handle_voice_command:160 | ✅ Successfully opened Safari
2025-07-12 11:50:51 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Открываю Safari' (voice: Milena)
2025-07-12 11:50:55 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'открываю Сафари'
2025-07-12 11:50:57 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открываю сафари' (from 1 fragments)
2025-07-12 11:50:59 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис закрой Сафари'
2025-07-12 11:51:01 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис закрой сафари' (from 1 fragments)
2025-07-12 11:51:01 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис закрой сафари'
2025-07-12 11:51:01 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'закрой сафари'
2025-07-12 11:51:01 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'закрой сафари'
2025-07-12 11:51:01 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: app_close
2025-07-12 11:51:01 | INFO     | __main__:_handle_voice_command:169 | 🔴 Closing application: Safari
2025-07-12 11:51:01 | INFO     | jarvis.modules.app_manager:close_application:201 | 🔴 Closing application: Safari
2025-07-12 11:51:02 | INFO     | jarvis.modules.app_manager:close_application:218 | ✅ Successfully closed Safari
2025-07-12 11:51:02 | INFO     | __main__:_handle_voice_command:173 | ✅ Successfully closed Safari
2025-07-12 11:51:02 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Закрываю Safari' (voice: Milena)
2025-07-12 11:51:05 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'закрываю Сафари'
2025-07-12 11:51:07 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'закрываю сафари' (from 1 fragments)
2025-07-12 11:51:23 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Привет Привет'
2025-07-12 11:51:25 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет привет' (from 1 fragments)
2025-07-12 11:51:28 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Открой Сафари'
2025-07-12 11:51:30 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открой сафари' (from 1 fragments)
2025-07-12 11:51:36 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'закрой Сафари'
2025-07-12 11:51:38 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'закрой сафари' (from 1 fragments)
2025-07-12 11:51:48 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Открой калькулятор'
2025-07-12 11:51:50 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открой калькулятор' (from 1 fragments)
2025-07-12 11:51:57 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис открой калькулятор'
2025-07-12 11:51:59 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис открой калькулятор' (from 1 fragments)
2025-07-12 11:51:59 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис открой калькулятор'
2025-07-12 11:51:59 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'открой калькулятор'
2025-07-12 11:51:59 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'открой калькулятор'
2025-07-12 11:51:59 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: app_open
2025-07-12 11:51:59 | INFO     | __main__:_handle_voice_command:156 | 🚀 Opening application: Калькулятор
2025-07-12 11:51:59 | INFO     | jarvis.modules.app_manager:open_application:156 | 🚀 Opening application: Калькулятор
2025-07-12 11:51:59 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 203:210: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:51:59 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 202:209: syntax error: Предполагаемый результат — конец строки и т. д.; полученный — идентификатор. (-2741)

2025-07-12 11:51:59 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 34:42: execution error: Не удается получить application "Калькулятор". (-1728)

2025-07-12 11:51:59 | ERROR    | jarvis.modules.app_manager:_run_applescript:52 | AppleScript error: 0:41: execution error: Unable to find application named 'Калькулятор' (1)

2025-07-12 11:51:59 | ERROR    | __main__:_handle_voice_command:164 | ❌ Failed to open Калькулятор
2025-07-12 11:51:59 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Не могу найти приложение Калькулятор' (voice: Milena)
2025-07-12 11:52:03 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'не могу найти приложение Калькулятор'
2025-07-12 11:52:05 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'не могу найти приложение калькулятор' (from 1 fragments)
2025-07-12 11:52:11 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис Test'
2025-07-12 11:52:13 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис test' (from 1 fragments)
2025-07-12 11:52:13 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис test'
2025-07-12 11:52:13 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'test'
2025-07-12 11:52:13 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'test'
2025-07-12 11:52:13 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: test
2025-07-12 11:52:13 | INFO     | __main__:_handle_voice_command:139 | ✅ Все системы работают нормально!
2025-07-12 11:52:13 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Все системы работают нормально!' (voice: Milena)
2025-07-12 11:52:17 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'все системы работают нормально'
2025-07-12 11:52:19 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'все системы работают нормально' (from 1 fragments)
2025-07-12 11:52:21 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис помощь'
2025-07-12 11:52:23 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис помощь' (from 1 fragments)
2025-07-12 11:52:23 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис помощь'
2025-07-12 11:52:23 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'помощь'
2025-07-12 11:52:23 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'помощь'
2025-07-12 11:52:23 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: help
2025-07-12 11:52:23 | INFO     | __main__:_handle_voice_command:150 | 💡 Показываю справку...
2025-07-12 11:52:23 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Доступные команды: привет, открой приложение, закрой приложение, тест, помощь, стоп' (voice: Milena)
2025-07-12 11:52:29 | INFO     | __main__:_show_help:213 | 💡 Доступные команды:
2025-07-12 11:52:29 | INFO     | __main__:_show_help:214 |    • 'Привет' - поприветствовать
2025-07-12 11:52:29 | INFO     | __main__:_show_help:215 |    • 'Открой [приложение]' - запустить приложение
2025-07-12 11:52:29 | INFO     | __main__:_show_help:216 |    • 'Закрой [приложение]' - закрыть приложение
2025-07-12 11:52:29 | INFO     | __main__:_show_help:217 |    • 'Тест' - проверить работу
2025-07-12 11:52:29 | INFO     | __main__:_show_help:218 |    • 'Помощь' - показать эту справку
2025-07-12 11:52:29 | INFO     | __main__:_show_help:219 |    • 'Стоп' - завершить работу
2025-07-12 11:52:32 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'доступные команды Привет Открой приложение Закрой приложение тест помощь стоп J'
2025-07-12 11:52:34 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'доступные команды привет открой приложение закрой приложение тест помощь стоп j' (from 1 fragments)
2025-07-12 11:52:41 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис стоп'
2025-07-12 11:52:43 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис стоп' (from 1 fragments)
2025-07-12 11:52:43 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис стоп'
2025-07-12 11:52:43 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'стоп'
2025-07-12 11:52:43 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'стоп'
2025-07-12 11:52:43 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: stop
2025-07-12 11:52:43 | INFO     | __main__:_handle_voice_command:144 | 🛑 Остановка по команде пользователя
2025-07-12 11:52:43 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Остановка по команде пользователя' (voice: Milena)
2025-07-12 11:52:46 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:52:46 | INFO     | jarvis.modules.voice:stop_listening:96 | 🔇 Stopped listening
2025-07-12 11:52:48 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'остановка по команде пользователя'
2025-07-12 11:52:48 | INFO     | jarvis.modules.voice:_continuous_listen_loop:157 | 🎧 Continuous listen loop ended
2025-07-12 11:52:48 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:52:48 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:52:48 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:52:48 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:52:48 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:52:48 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:52:48 | INFO     | __main__:initialize:27 | Initializing Jarvis AI Assistant...
2025-07-12 11:52:48 | INFO     | jarvis.modules.voice:initialize:44 | Initializing Voice Module...
2025-07-12 11:52:48 | INFO     | jarvis.modules.voice:initialize:45 | 🔄 Using fully local speech recognition
2025-07-12 11:52:48 | INFO     | jarvis.modules.voice:initialize:59 | Calibrating microphone for ambient noise...
2025-07-12 11:52:50 | INFO     | jarvis.modules.voice:initialize:61 | Energy threshold set to: 25.62965650498005
2025-07-12 11:52:50 | INFO     | jarvis.modules.voice:initialize:63 | Voice Module initialized successfully
2025-07-12 11:52:50 | INFO     | __main__:initialize:52 | Module VoiceModule initialized successfully
2025-07-12 11:52:50 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:52:50 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:52:50 | INFO     | __main__:initialize:52 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:52:50 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:52:50 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:52:51 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:52:51 | INFO     | __main__:initialize:52 | Module AppManager initialized successfully
2025-07-12 11:52:51 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:52:51 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:52:51 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:52:51 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:52:51 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:52:51 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:52:51 | INFO     | __main__:initialize:52 | Module TTSModule initialized successfully
2025-07-12 11:52:51 | INFO     | __main__:initialize:58 | Jarvis initialization complete!
2025-07-12 11:52:51 | INFO     | __main__:start:65 | Jarvis is now listening...
2025-07-12 11:52:51 | INFO     | __main__:start:66 | Say 'jarvis' to activate
2025-07-12 11:52:51 | INFO     | jarvis.modules.voice:start_listening:85 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 11:52:51 | INFO     | jarvis.modules.voice:_continuous_listen_loop:104 | 🎧 Starting optimized continuous listen loop...
2025-07-12 11:52:51 | INFO     | jarvis.modules.voice:_continuous_listen_loop:109 | 🎤 Microphone opened for continuous listening
2025-07-12 11:52:58 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис'
2025-07-12 11:53:00 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис' (from 1 fragments)
2025-07-12 11:53:00 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис'
2025-07-12 11:53:00 | INFO     | jarvis.modules.voice:_process_voice_input:246 | 👂 Wake word only - responding and waiting for command...
2025-07-12 11:53:00 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: '__WAKE_WORD_ACTIVATED__'
2025-07-12 11:53:00 | INFO     | __main__:_handle_voice_command:109 | 👂 Да, слушаю вас!
2025-07-12 11:53:00 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Да, слушаю вас!' (voice: Milena)
2025-07-12 11:53:04 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Да слушаю вас'
2025-07-12 11:53:06 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'да слушаю вас' (from 1 fragments)
2025-07-12 11:53:06 | INFO     | jarvis.modules.voice:_process_voice_input:258 | 📝 Command received after wake word: 'да слушаю вас'
2025-07-12 11:53:06 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'да слушаю вас'
2025-07-12 11:53:06 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: unknown
2025-07-12 11:53:06 | INFO     | __main__:_handle_voice_command:182 | 🤔 Команда 'да слушаю вас' не распознана
2025-07-12 11:53:06 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Извините, я не понял команду. Скажите помощь для списка команд.' (voice: Milena)
2025-07-12 11:53:12 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Открой Telegram'
2025-07-12 11:53:14 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открой telegram' (from 1 fragments)
2025-07-12 11:53:15 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Открой Telegram'
2025-07-12 11:53:17 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открой telegram' (from 1 fragments)
2025-07-12 11:53:23 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис открой Telegram'
2025-07-12 11:53:25 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис открой telegram' (from 1 fragments)
2025-07-12 11:53:25 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис открой telegram'
2025-07-12 11:53:25 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'открой telegram'
2025-07-12 11:53:25 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'открой telegram'
2025-07-12 11:53:25 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: app_open
2025-07-12 11:53:25 | INFO     | __main__:_handle_voice_command:156 | 🚀 Opening application: Telegram
2025-07-12 11:53:25 | INFO     | jarvis.modules.app_manager:open_application:156 | 🚀 Opening application: Telegram
2025-07-12 11:53:26 | INFO     | jarvis.modules.app_manager:open_application:181 | ✅ Successfully opened Telegram
2025-07-12 11:53:26 | INFO     | __main__:_handle_voice_command:160 | ✅ Successfully opened Telegram
2025-07-12 11:53:26 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Открываю Telegram' (voice: Milena)
2025-07-12 11:53:29 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'открываю Telegram'
2025-07-12 11:53:31 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'открываю telegram' (from 1 fragments)
2025-07-12 11:53:38 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис закрой Telegram'
2025-07-12 11:53:40 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис закрой telegram' (from 1 fragments)
2025-07-12 11:53:40 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис закрой telegram'
2025-07-12 11:53:40 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'закрой telegram'
2025-07-12 11:53:40 | INFO     | __main__:_handle_voice_command:103 | 🎤 Processing voice command: 'закрой telegram'
2025-07-12 11:53:40 | INFO     | __main__:_handle_voice_command:123 | 🧠 Detected intent: app_close
2025-07-12 11:53:40 | INFO     | __main__:_handle_voice_command:169 | 🔴 Closing application: Telegram
2025-07-12 11:53:40 | INFO     | jarvis.modules.app_manager:close_application:201 | 🔴 Closing application: Telegram
2025-07-12 11:53:40 | INFO     | jarvis.modules.app_manager:close_application:218 | ✅ Successfully closed Telegram
2025-07-12 11:53:40 | INFO     | __main__:_handle_voice_command:173 | ✅ Successfully closed Telegram
2025-07-12 11:53:40 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Закрываю Telegram' (voice: Milena)
2025-07-12 11:53:44 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'закрываю Telegram'
2025-07-12 11:53:46 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'закрываю telegram' (from 1 fragments)
2025-07-12 11:53:46 | INFO     | __main__:signal_handler:227 | Received signal 2
2025-07-12 11:53:46 | INFO     | __main__:shutdown:89 | Shutting down Jarvis...
2025-07-12 11:53:46 | INFO     | jarvis.modules.voice:stop_listening:96 | 🔇 Stopped listening
2025-07-12 11:53:48 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:53:48 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:53:48 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:53:48 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:53:48 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:53:48 | INFO     | __main__:shutdown:99 | Jarvis shutdown complete
2025-07-12 11:59:11 | INFO     | __main__:initialize:28 | Initializing Jarvis AI Assistant...
2025-07-12 11:59:11 | INFO     | jarvis.modules.voice:initialize:44 | Initializing Voice Module...
2025-07-12 11:59:11 | INFO     | jarvis.modules.voice:initialize:45 | 🔄 Using fully local speech recognition
2025-07-12 11:59:12 | INFO     | jarvis.modules.voice:initialize:59 | Calibrating microphone for ambient noise...
2025-07-12 11:59:14 | INFO     | jarvis.modules.voice:initialize:61 | Energy threshold set to: 167.28769073383947
2025-07-12 11:59:14 | INFO     | jarvis.modules.voice:initialize:63 | Voice Module initialized successfully
2025-07-12 11:59:14 | INFO     | __main__:initialize:55 | Module VoiceModule initialized successfully
2025-07-12 11:59:14 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 11:59:14 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 11:59:14 | INFO     | __main__:initialize:55 | Module LocalCommandProcessor initialized successfully
2025-07-12 11:59:14 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 11:59:14 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 11:59:14 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 11:59:14 | INFO     | __main__:initialize:55 | Module AppManager initialized successfully
2025-07-12 11:59:14 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 11:59:14 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 11:59:14 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 11:59:14 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 11:59:14 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 11:59:14 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 11:59:14 | INFO     | __main__:initialize:55 | Module TTSModule initialized successfully
2025-07-12 11:59:14 | INFO     | jarvis.modules.ai_brain:initialize:42 | Initializing AI Brain Module...
2025-07-12 11:59:14 | INFO     | jarvis.modules.ai_brain:initialize:56 | 🧠 AI Brain Module initialized successfully
2025-07-12 11:59:14 | INFO     | __main__:initialize:55 | Module AIBrain initialized successfully
2025-07-12 11:59:14 | INFO     | __main__:initialize:61 | Jarvis initialization complete!
2025-07-12 11:59:14 | INFO     | __main__:start:68 | Jarvis is now listening...
2025-07-12 11:59:14 | INFO     | __main__:start:69 | Say 'jarvis' to activate
2025-07-12 11:59:14 | INFO     | jarvis.modules.voice:start_listening:85 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 11:59:14 | INFO     | jarvis.modules.voice:_continuous_listen_loop:104 | 🎧 Starting optimized continuous listen loop...
2025-07-12 11:59:15 | INFO     | jarvis.modules.voice:_continuous_listen_loop:109 | 🎤 Microphone opened for continuous listening
2025-07-12 11:59:29 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Привет'
2025-07-12 11:59:31 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет' (from 1 fragments)
2025-07-12 11:59:38 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис Привет'
2025-07-12 11:59:40 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис привет' (from 1 fragments)
2025-07-12 11:59:40 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис привет'
2025-07-12 11:59:40 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'привет'
2025-07-12 11:59:40 | INFO     | __main__:_handle_voice_command:106 | 🎤 Processing voice command: 'привет'
2025-07-12 11:59:40 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'привет'
2025-07-12 11:59:40 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Привет! Рад вас видеть!'
2025-07-12 11:59:40 | INFO     | __main__:_handle_voice_command:124 | 🧠 AI Response: Привет! Рад вас видеть!
2025-07-12 11:59:40 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Привет! Рад вас видеть!' (voice: Milena)
2025-07-12 11:59:43 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Привет Рад вас видеть'
2025-07-12 11:59:45 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет рад вас видеть' (from 1 fragments)
2025-07-12 11:59:46 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Как у тебя дела'
2025-07-12 11:59:48 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'как у тебя дела' (from 1 fragments)
2025-07-12 11:59:53 | INFO     | __main__:signal_handler:263 | Received signal 2
2025-07-12 11:59:53 | INFO     | __main__:shutdown:92 | Shutting down Jarvis...
2025-07-12 11:59:53 | INFO     | jarvis.modules.voice:stop_listening:96 | 🔇 Stopped listening
2025-07-12 11:59:55 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 11:59:55 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 11:59:55 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 11:59:55 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 11:59:55 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 11:59:55 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AIBrain
2025-07-12 11:59:55 | INFO     | __main__:shutdown:102 | Jarvis shutdown complete
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:13 | 🧪 Starting AI Brain Test
2025-07-12 12:00:17 | INFO     | jarvis.modules.ai_brain:initialize:42 | Initializing AI Brain Module...
2025-07-12 12:00:17 | INFO     | jarvis.modules.ai_brain:initialize:56 | 🧠 AI Brain Module initialized successfully
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:23 | ✅ AI Brain initialized successfully
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:39 | 🗣️ Testing AI responses:
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:42 | 
--- Test 1/10 ---
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:43 | 👤 User: Привет!
2025-07-12 12:00:17 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Привет!'
2025-07-12 12:00:17 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Добро пожаловать! Готов к работе!'
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Добро пожаловать! Готов к работе!
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:42 | 
--- Test 2/10 ---
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:43 | 👤 User: Как дела?
2025-07-12 12:00:17 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Как дела?'
2025-07-12 12:00:17 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Прекрасно! Готов к новым задачам. Что планируете? Хорошего дня!'
2025-07-12 12:00:17 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Прекрасно! Готов к новым задачам. Что планируете? Хорошего дня!
2025-07-12 12:00:18 | INFO     | __main__:test_ai_brain:42 | 
--- Test 3/10 ---
2025-07-12 12:00:18 | INFO     | __main__:test_ai_brain:43 | 👤 User: Расскажи о себе
2025-07-12 12:00:18 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Расскажи о себе'
2025-07-12 12:00:18 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'С удовольствием расскажу! О чем именно хотите узнать?'
2025-07-12 12:00:18 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: С удовольствием расскажу! О чем именно хотите узнать?
2025-07-12 12:00:18 | INFO     | __main__:test_ai_brain:42 | 
--- Test 4/10 ---
2025-07-12 12:00:18 | INFO     | __main__:test_ai_brain:43 | 👤 User: Что ты умеешь?
2025-07-12 12:00:18 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Что ты умеешь?'
2025-07-12 12:00:18 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Я Джарвис - ваш персональный ИИ-помощник. Умею: голосовое управление, открытие приложений, естественное общение, помощь в задачах.'
2025-07-12 12:00:18 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Я Джарвис - ваш персональный ИИ-помощник. Умею: голосовое управление, открытие приложений, естественное общение, помощь в задачах.
2025-07-12 12:00:19 | INFO     | __main__:test_ai_brain:42 | 
--- Test 5/10 ---
2025-07-12 12:00:19 | INFO     | __main__:test_ai_brain:43 | 👤 User: Какой сейчас год?
2025-07-12 12:00:19 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Какой сейчас год?'
2025-07-12 12:00:19 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Сейчас 2025 год.'
2025-07-12 12:00:19 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Сейчас 2025 год.
2025-07-12 12:00:19 | INFO     | __main__:test_ai_brain:42 | 
--- Test 6/10 ---
2025-07-12 12:00:19 | INFO     | __main__:test_ai_brain:43 | 👤 User: Как тебя зовут?
2025-07-12 12:00:19 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Как тебя зовут?'
2025-07-12 12:00:19 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Хороший вопрос, анализирую... Хороший вопрос! Я могу помочь разобраться. Уточните детали?'
2025-07-12 12:00:19 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Хороший вопрос, анализирую... Хороший вопрос! Я могу помочь разобраться. Уточните детали?
2025-07-12 12:00:20 | INFO     | __main__:test_ai_brain:42 | 
--- Test 7/10 ---
2025-07-12 12:00:20 | INFO     | __main__:test_ai_brain:43 | 👤 User: Спасибо за помощь
2025-07-12 12:00:20 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Спасибо за помощь'
2025-07-12 12:00:20 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Рад был помочь!'
2025-07-12 12:00:20 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Рад был помочь!
2025-07-12 12:00:20 | INFO     | __main__:test_ai_brain:42 | 
--- Test 8/10 ---
2025-07-12 12:00:20 | INFO     | __main__:test_ai_brain:43 | 👤 User: Что нового?
2025-07-12 12:00:20 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Что нового?'
2025-07-12 12:00:20 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Все хорошо! Изучаю новое и совершенствуюсь. А вы как? Хорошего дня!'
2025-07-12 12:00:20 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Все хорошо! Изучаю новое и совершенствуюсь. А вы как? Хорошего дня!
2025-07-12 12:00:21 | INFO     | __main__:test_ai_brain:42 | 
--- Test 9/10 ---
2025-07-12 12:00:21 | INFO     | __main__:test_ai_brain:43 | 👤 User: Помоги мне
2025-07-12 12:00:21 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'Помоги мне'
2025-07-12 12:00:21 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Конечно, помогу! Расскажите подробнее, что нужно сделать? Кстати, это уже наше 10-е общение!'
2025-07-12 12:00:21 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Конечно, помогу! Расскажите подробнее, что нужно сделать? Кстати, это уже наше 10-е общение!
2025-07-12 12:00:22 | INFO     | __main__:test_ai_brain:42 | 
--- Test 10/10 ---
2025-07-12 12:00:22 | INFO     | __main__:test_ai_brain:43 | 👤 User: До свидания
2025-07-12 12:00:22 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'До свидания'
2025-07-12 12:00:22 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Пока! Обращайтесь, если что-то понадобится!'
2025-07-12 12:00:22 | INFO     | __main__:test_ai_brain:47 | 🤖 AI: Пока! Обращайтесь, если что-то понадобится!
2025-07-12 12:00:22 | INFO     | __main__:test_ai_brain:56 | 
📊 Conversation Summary:
2025-07-12 12:00:22 | INFO     | __main__:test_ai_brain:57 |    Total interactions: 10
2025-07-12 12:00:22 | INFO     | __main__:test_ai_brain:58 |    Recent topics: ['greeting', 'gratitude', 'farewell', 'time']
2025-07-12 12:00:22 | INFO     | __main__:test_ai_brain:59 |    Dominant emotion: neutral
2025-07-12 12:00:22 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AIBrain
2025-07-12 12:00:22 | INFO     | __main__:test_ai_brain:63 | 
🎉 AI Brain test completed!
2025-07-12 12:01:26 | INFO     | __main__:initialize:28 | Initializing Jarvis AI Assistant...
2025-07-12 12:01:26 | INFO     | jarvis.modules.voice:initialize:44 | Initializing Voice Module...
2025-07-12 12:01:26 | INFO     | jarvis.modules.voice:initialize:45 | 🔄 Using fully local speech recognition
2025-07-12 12:01:26 | INFO     | jarvis.modules.voice:initialize:59 | Calibrating microphone for ambient noise...
2025-07-12 12:01:28 | INFO     | jarvis.modules.voice:initialize:61 | Energy threshold set to: 160.10524377993207
2025-07-12 12:01:29 | INFO     | jarvis.modules.voice:initialize:63 | Voice Module initialized successfully
2025-07-12 12:01:29 | INFO     | __main__:initialize:55 | Module VoiceModule initialized successfully
2025-07-12 12:01:29 | INFO     | jarvis.modules.command_processor:initialize:98 | Initializing Local Command Processor...
2025-07-12 12:01:29 | INFO     | jarvis.modules.command_processor:initialize:100 | Local Command Processor initialized successfully
2025-07-12 12:01:29 | INFO     | __main__:initialize:55 | Module LocalCommandProcessor initialized successfully
2025-07-12 12:01:29 | INFO     | jarvis.modules.app_manager:initialize:21 | Initializing App Manager...
2025-07-12 12:01:29 | INFO     | jarvis.modules.app_manager:initialize:26 | ✅ AppleScript is available
2025-07-12 12:01:29 | INFO     | jarvis.modules.app_manager:initialize:32 | App Manager initialized successfully
2025-07-12 12:01:29 | INFO     | __main__:initialize:55 | Module AppManager initialized successfully
2025-07-12 12:01:29 | INFO     | jarvis.modules.tts:initialize:23 | Initializing TTS Module...
2025-07-12 12:01:29 | INFO     | jarvis.modules.tts:_test_say_command:54 | ✅ macOS 'say' command is available
2025-07-12 12:01:29 | INFO     | jarvis.modules.tts:_get_available_voices:87 | Available Russian voices: ['Milena']
2025-07-12 12:01:29 | INFO     | jarvis.modules.tts:_get_available_voices:88 | Available English voices: ['Albert', 'Bad', 'Bahh']...
2025-07-12 12:01:29 | INFO     | jarvis.modules.tts:_get_available_voices:100 | Selected voice: Milena
2025-07-12 12:01:29 | INFO     | jarvis.modules.tts:initialize:35 | ✅ TTS Module initialized successfully
2025-07-12 12:01:29 | INFO     | __main__:initialize:55 | Module TTSModule initialized successfully
2025-07-12 12:01:29 | INFO     | jarvis.modules.ai_brain:initialize:42 | Initializing AI Brain Module...
2025-07-12 12:01:29 | INFO     | jarvis.modules.ai_brain:initialize:56 | 🧠 AI Brain Module initialized successfully
2025-07-12 12:01:29 | INFO     | __main__:initialize:55 | Module AIBrain initialized successfully
2025-07-12 12:01:29 | INFO     | __main__:initialize:61 | Jarvis initialization complete!
2025-07-12 12:01:29 | INFO     | __main__:start:68 | Jarvis is now listening...
2025-07-12 12:01:29 | INFO     | __main__:start:69 | Say 'jarvis' to activate
2025-07-12 12:01:29 | INFO     | jarvis.modules.voice:start_listening:85 | 🎤 Started continuous listening for wake word: 'jarvis'
2025-07-12 12:01:29 | INFO     | jarvis.modules.voice:_continuous_listen_loop:104 | 🎧 Starting optimized continuous listen loop...
2025-07-12 12:01:29 | INFO     | jarvis.modules.voice:_continuous_listen_loop:109 | 🎤 Microphone opened for continuous listening
2025-07-12 12:01:34 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Привет'
2025-07-12 12:01:36 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'привет' (from 1 fragments)
2025-07-12 12:01:42 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис Привет'
2025-07-12 12:01:44 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис привет' (from 1 fragments)
2025-07-12 12:01:44 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис привет'
2025-07-12 12:01:44 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'привет'
2025-07-12 12:01:44 | INFO     | __main__:_handle_voice_command:106 | 🎤 Processing voice command: 'привет'
2025-07-12 12:01:44 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'привет'
2025-07-12 12:01:44 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Здравствуйте! Чем могу помочь?'
2025-07-12 12:01:44 | INFO     | __main__:_handle_voice_command:124 | 🧠 AI Response: Здравствуйте! Чем могу помочь?
2025-07-12 12:01:44 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Здравствуйте! Чем могу помочь?' (voice: Milena)
2025-07-12 12:01:48 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Здравствуйте Чем могу помочь'
2025-07-12 12:01:50 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'здравствуйте чем могу помочь' (from 1 fragments)
2025-07-12 12:01:53 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'сегодня год'
2025-07-12 12:01:55 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'сегодня год' (from 1 fragments)
2025-07-12 12:02:02 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Какой сейчас год'
2025-07-12 12:02:04 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'какой сейчас год' (from 1 fragments)
2025-07-12 12:02:07 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'Джарвис Какой сейчас год'
2025-07-12 12:02:09 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'джарвис какой сейчас год' (from 1 fragments)
2025-07-12 12:02:09 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'джарвис какой сейчас год'
2025-07-12 12:02:09 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'какой сейчас год'
2025-07-12 12:02:10 | INFO     | __main__:_handle_voice_command:106 | 🎤 Processing voice command: 'какой сейчас год'
2025-07-12 12:02:10 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'какой сейчас год'
2025-07-12 12:02:10 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Сейчас 2025 год.'
2025-07-12 12:02:10 | INFO     | __main__:_handle_voice_command:124 | 🧠 AI Response: Сейчас 2025 год.
2025-07-12 12:02:10 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Сейчас 2025 год.' (voice: Milena)
2025-07-12 12:02:17 | INFO     | jarvis.modules.voice:_recognize_speech:179 | 🎯 Recognized speech: 'сейчас 2025 год Джарвис что ты умеешь'
2025-07-12 12:02:19 | INFO     | jarvis.modules.voice:_process_speech_buffer:210 | 🔗 Combined speech: 'сейчас 2025 год джарвис что ты умеешь' (from 1 fragments)
2025-07-12 12:02:19 | INFO     | jarvis.modules.voice:_process_voice_input:233 | 🎯 Wake word detected in: 'сейчас 2025 год джарвис что ты умеешь'
2025-07-12 12:02:19 | INFO     | jarvis.modules.voice:_process_voice_input:241 | 📝 Immediate command: 'что ты умеешь'
2025-07-12 12:02:19 | INFO     | __main__:_handle_voice_command:106 | 🎤 Processing voice command: 'что ты умеешь'
2025-07-12 12:02:19 | INFO     | jarvis.modules.ai_brain:process_message:204 | 🧠 Processing message: 'что ты умеешь'
2025-07-12 12:02:19 | INFO     | jarvis.modules.ai_brain:process_message:218 | 🗣️ Generated response: 'Я Джарвис - ваш персональный ИИ-помощник. Умею: голосовое управление, открытие приложений, естественное общение, помощь в задачах.'
2025-07-12 12:02:19 | INFO     | __main__:_handle_voice_command:124 | 🧠 AI Response: Я Джарвис - ваш персональный ИИ-помощник. Умею: голосовое управление, открытие приложений, естественное общение, помощь в задачах.
2025-07-12 12:02:19 | INFO     | jarvis.modules.tts:speak:116 | 🗣️ Speaking: 'Я Джарвис - ваш персональный ИИ-помощник. Умею: голосовое управление, открытие приложений, естественное общение, помощь в задачах.' (voice: Milena)
2025-07-12 12:02:25 | INFO     | __main__:signal_handler:263 | Received signal 2
2025-07-12 12:02:25 | ERROR    | jarvis.modules.tts:speak:134 | ❌ Speech synthesis failed: Unknown error
2025-07-12 12:02:26 | INFO     | __main__:shutdown:92 | Shutting down Jarvis...
2025-07-12 12:02:26 | INFO     | jarvis.modules.voice:stop_listening:96 | 🔇 Stopped listening
2025-07-12 12:02:28 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: VoiceModule
2025-07-12 12:02:28 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: LocalCommandProcessor
2025-07-12 12:02:28 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AppManager
2025-07-12 12:02:28 | INFO     | jarvis.modules.tts:stop_speaking:152 | 🔇 Stopped speaking
2025-07-12 12:02:28 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: TTSModule
2025-07-12 12:02:28 | INFO     | jarvis.core.base:cleanup:34 | Cleaning up module: AIBrain
2025-07-12 12:02:28 | INFO     | __main__:shutdown:102 | Jarvis shutdown complete
