"""
Модуль распознавания голоса для Jarvis
"""

import asyncio
import speech_recognition as sr
import threading
import queue
import time
from typing import Optional, Callable
from jarvis.core.base import BaseModule, CommandResult
from jarvis.config import config

class VoiceModule(BaseModule):
    """Модуль для распознавания голоса и обработки голосовых команд"""

    def __init__(self):
        super().__init__("VoiceModule")
        self.recognizer = sr.Recognizer()
        self.microphone = None

        # Состояния модуля
        self.is_listening = False
        self.is_processing = False
        self.is_speaking = False  # Флаг для отключения микрофона во время TTS
        self.listening_state = "idle"  # idle, listening, processing, waiting_command, muted, active_dialog

        # Режим активного диалога
        self.active_dialog_mode = False
        self.last_command_time = 0
        self.dialog_timeout = 10.0  # Секунды до автоматического выхода из диалога
        self.dialog_timer = None

        # Буферизация для объединения фрагментов речи
        self.speech_buffer = []
        self.last_speech_time = 0
        self.buffer_timeout = 2.0  # Секунды для объединения фрагментов

        # Очереди и callback'и
        self.command_callback: Optional[Callable] = None
        self.command_queue = queue.Queue()

        # Потоки
        self.listen_thread = None
        self.stop_event = threading.Event()
        
    async def initialize(self) -> bool:
        """Инициализация модуля распознавания голоса"""
        try:
            self.logger.info("Initializing Voice Module...")
            self.logger.info("🔄 Using fully local speech recognition")

            # Оптимизированные настройки для лучшего распознавания фраз
            self.recognizer.energy_threshold = 300  # Умеренный порог
            self.recognizer.dynamic_energy_threshold = True  # Автоматическая настройка
            self.recognizer.pause_threshold = 1.5  # Увеличенная пауза между фразами (было 1.0)
            self.recognizer.phrase_threshold = 0.2  # Минимальная длина фразы (было 0.3)
            self.recognizer.non_speaking_duration = 1.2  # Больше времени тишины (было 0.8)

            # Инициализируем микрофон
            self.microphone = sr.Microphone()

            # Калибруем микрофон для шума окружения
            with self.microphone as source:
                self.logger.info("Calibrating microphone for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
                self.logger.info(f"Energy threshold set to: {self.recognizer.energy_threshold}")

            self.logger.info("Voice Module initialized successfully")
            self.is_initialized = True
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Voice Module: {e}")
            return False
    


    def set_command_callback(self, callback: Callable):
        """Установка callback функции для обработки команд"""
        self.command_callback = callback

    async def start_listening(self):
        """Запуск прослушивания голосовых команд"""
        if self.is_listening:
            return

        self.is_listening = True
        self.stop_event.clear()
        self.listening_state = "listening"
        self.logger.info(f"🎤 Started continuous listening for wake word: '{config.wake_word}'")

        # Запускаем прослушивание в отдельном потоке
        self.listen_thread = threading.Thread(target=self._continuous_listen_loop, daemon=True)
        self.listen_thread.start()

    def stop_listening(self):
        """Остановка прослушивания"""
        self.is_listening = False
        self.listening_state = "idle"
        self.stop_event.set()
        self.logger.info("🔇 Stopped listening")

        # Ждем завершения потока
        if self.listen_thread and self.listen_thread.is_alive():
            self.listen_thread.join(timeout=2)
    
    def _continuous_listen_loop(self):
        """Непрерывный цикл прослушивания с оптимизированной логикой"""
        self.logger.info("🎧 Starting optimized continuous listen loop...")

        try:
            # Открываем микрофон один раз для всего сеанса
            with self.microphone as source:
                self.logger.info("🎤 Microphone opened for continuous listening")

                # Быстрая калибровка только в начале
                self.recognizer.adjust_for_ambient_noise(source, duration=0.5)
                self.logger.debug(f"🔧 Energy threshold: {self.recognizer.energy_threshold}")

                while self.is_listening and not self.stop_event.is_set():
                    try:
                        # Проверяем, не говорит ли система сама
                        if self.is_speaking:
                            self._update_listening_state("muted")
                            time.sleep(0.1)
                            continue

                        self._update_listening_state("listening")

                        # Слушаем аудио с оптимизированными параметрами для длинных фраз
                        audio = self.recognizer.listen(
                            source,
                            timeout=1,  # Короткий timeout для проверки stop_event
                            phrase_time_limit=config.voice_recognition_phrase_timeout
                        )

                        # Еще раз проверяем, не начала ли система говорить
                        if self.is_speaking:
                            self.logger.debug("🔇 Ignoring audio - system is speaking")
                            continue

                        self._update_listening_state("processing")
                        self.logger.debug("🔊 Audio captured, processing...")

                        # Распознаем речь в отдельном потоке для неблокирующей работы
                        text = self._recognize_speech(audio)
                        if text and not self.is_speaking:  # Дополнительная проверка
                            self._process_voice_input_with_buffer(text)

                    except sr.WaitTimeoutError:
                        # Нормальная ситуация - просто продолжаем слушать
                        continue
                    except Exception as e:
                        self.logger.error(f"Error in listen loop: {e}")
                        time.sleep(0.1)  # Короткая пауза при ошибке
                        continue

        except Exception as e:
            self.logger.error(f"Critical error in listen loop: {e}")
        finally:
            self._update_listening_state("idle")
            self.logger.info("🎧 Continuous listen loop ended")
    
    def _update_listening_state(self, new_state: str):
        """Обновление состояния прослушивания с логированием"""
        if self.listening_state != new_state:
            self.listening_state = new_state
            state_icons = {
                "idle": "💤",
                "listening": "👂",
                "processing": "🧠",
                "waiting_command": "⏳",
                "muted": "🔇",
                "active_dialog": "💬"
            }
            icon = state_icons.get(new_state, "❓")
            self.logger.debug(f"{icon} State: {new_state}")

    def _recognize_speech(self, audio) -> Optional[str]:
        """Распознавание речи из аудио"""
        try:
            # Используем Google Speech Recognition (бесплатный)
            self.logger.debug("🔍 Attempting speech recognition...")
            text = self.recognizer.recognize_google(audio, language=config.language)
            self.logger.info(f"🎯 Recognized speech: '{text}'")
            return text.lower()
        except sr.UnknownValueError:
            # Речь не распознана - это нормально
            self.logger.debug("🔇 No speech recognized")
            return None
        except sr.RequestError as e:
            self.logger.error(f"❌ Speech recognition error: {e}")
            return None

    def _process_voice_input_with_buffer(self, text: str):
        """Обработка речи с буферизацией для объединения фрагментов"""
        current_time = time.time()

        # Добавляем текст в буфер
        self.speech_buffer.append(text.strip())
        self.last_speech_time = current_time

        self.logger.debug(f"📝 Added to buffer: '{text}' (buffer size: {len(self.speech_buffer)})")

        # Запускаем таймер для обработки буфера
        threading.Timer(self.buffer_timeout, self._process_speech_buffer).start()

    def _process_speech_buffer(self):
        """Обработка накопленного буфера речи"""
        current_time = time.time()

        # Проверяем, прошло ли достаточно времени с последней речи
        if current_time - self.last_speech_time >= self.buffer_timeout and self.speech_buffer:
            # Объединяем все фрагменты в одну фразу
            combined_text = " ".join(self.speech_buffer).strip()
            self.logger.info(f"🔗 Combined speech: '{combined_text}' (from {len(self.speech_buffer)} fragments)")

            # Очищаем буфер
            self.speech_buffer.clear()

            # Обрабатываем объединенную фразу
            self._process_voice_input(combined_text)

    def _process_voice_input(self, text: str):
        """Обработка распознанного текста с режимом активного диалога"""
        text_lower = text.lower().strip()

        # Расширенный список wake words для лучшего распознавания
        wake_words = [
            config.wake_word.lower(),
            "джарвис", "jarvis", "jarv", "жарвис",
            "дарвис", "джарв", "жарв"
        ]

        # Команды для выхода из диалога
        stop_commands = ["стоп", "stop", "хватит", "достаточно", "закончи", "выход"]

        # Проверяем команды остановки диалога
        if self.active_dialog_mode and any(stop_cmd in text_lower for stop_cmd in stop_commands):
            self.logger.info(f"🛑 Dialog stop command detected: '{text}'")
            self._exit_dialog_mode()
            self.command_queue.put("__DIALOG_STOPPED__")
            return

        # Проверяем наличие wake word в тексте
        wake_word_found = any(wake_word in text_lower for wake_word in wake_words)

        if wake_word_found:
            self.logger.info(f"🎯 Wake word detected in: '{text}'")

            # Извлекаем команду после wake word
            command = self._extract_command_after_wake_word(text_lower, wake_words)

            if command:
                # Команда была в той же фразе
                self.logger.info(f"📝 Immediate command: '{command}'")
                self.command_queue.put(command)
                self._enter_dialog_mode()
            else:
                # Только wake word - активируем диалог
                self.logger.info("👂 Wake word only - activating dialog mode...")
                self.command_queue.put("__WAKE_WORD_ACTIVATED__")
                self._enter_dialog_mode()

        elif self.active_dialog_mode:
            # Мы в режиме активного диалога - принимаем любые команды
            self.logger.info(f"💬 Dialog command: '{text}'")
            self.command_queue.put(text)
            self._refresh_dialog_timer()

        elif self.is_processing:
            # Старая логика для совместимости
            self.logger.info(f"📝 Command received after wake word: '{text}'")
            self.command_queue.put(text)
            self.is_processing = False
            self._update_listening_state("listening")
        else:
            # Обычная речь без wake word - игнорируем
            self.logger.debug(f"🔇 Ignored speech (no wake word): '{text}'")

    def _extract_command_after_wake_word(self, text: str, wake_words: list) -> Optional[str]:
        """Извлечение команды из текста после wake word"""
        for wake_word in wake_words:
            if wake_word in text:
                # Находим позицию wake word и берем текст после него
                wake_pos = text.find(wake_word)
                command_start = wake_pos + len(wake_word)
                command = text[command_start:].strip()

                # Убираем возможные артикли и предлоги в начале
                command = command.lstrip("., ")

                if len(command) > 2:  # Минимальная длина команды
                    return command
        return None

    def _enter_dialog_mode(self):
        """Вход в режим активного диалога"""
        self.active_dialog_mode = True
        self.last_command_time = time.time()
        self._update_listening_state("active_dialog")
        self.logger.info("💬 Entered active dialog mode")

        # Запускаем таймер автоматического выхода
        self._refresh_dialog_timer()

    def _exit_dialog_mode(self):
        """Выход из режима активного диалога"""
        self.active_dialog_mode = False
        self.is_processing = False
        self._update_listening_state("listening")
        self.logger.info("💤 Exited active dialog mode")

        # Отменяем таймер
        if self.dialog_timer:
            self.dialog_timer.cancel()
            self.dialog_timer = None

    def _refresh_dialog_timer(self):
        """Обновление таймера диалога"""
        # Отменяем предыдущий таймер
        if self.dialog_timer:
            self.dialog_timer.cancel()

        # Запускаем новый таймер
        self.dialog_timer = threading.Timer(self.dialog_timeout, self._dialog_timeout_handler)
        self.dialog_timer.start()
        self.logger.debug(f"⏰ Dialog timer refreshed ({self.dialog_timeout}s)")

    def _dialog_timeout_handler(self):
        """Обработчик таймаута диалога"""
        if self.active_dialog_mode:
            self.logger.info("⏰ Dialog timeout - exiting active mode")
            self._exit_dialog_mode()
            self.command_queue.put("__DIALOG_TIMEOUT__")

    def _reset_command_waiting(self):
        """Сброс состояния ожидания команды по таймауту"""
        if self.is_processing:
            self.logger.info("⏰ Command waiting timeout - resetting state")
            self.is_processing = False
            self._update_listening_state("listening")

    def get_command(self) -> Optional[str]:
        """Получить команду из очереди (неблокирующий вызов)"""
        try:
            return self.command_queue.get_nowait()
        except queue.Empty:
            return None

    def get_listening_state(self) -> str:
        """Получить текущее состояние прослушивания"""
        return self.listening_state

    def is_ready_for_commands(self) -> bool:
        """Проверить, готов ли модуль принимать команды"""
        return self.is_initialized and self.is_listening

    def is_in_dialog_mode(self) -> bool:
        """Проверить, находится ли система в режиме активного диалога"""
        return self.active_dialog_mode

    def get_dialog_time_remaining(self) -> float:
        """Получить оставшееся время диалога"""
        if not self.active_dialog_mode:
            return 0.0
        elapsed = time.time() - self.last_command_time
        return max(0.0, self.dialog_timeout - elapsed)

    def set_speaking_state(self, is_speaking: bool):
        """Установить состояние воспроизведения TTS"""
        self.is_speaking = is_speaking
        if is_speaking:
            self.logger.debug("🔇 Microphone muted - system is speaking")
        else:
            self.logger.debug("🎤 Microphone unmuted - ready to listen")

    async def execute(self, command: str, **kwargs) -> CommandResult:
        """Выполнение команды модулем"""
        try:
            if command == "start_listening":
                await self.start_listening()
                return CommandResult(True, "Voice listening started")
            elif command == "stop_listening":
                self.stop_listening()
                return CommandResult(True, "Voice listening stopped")
            elif command == "get_state":
                state_info = {
                    "listening": self.is_listening,
                    "state": self.listening_state,
                    "processing": self.is_processing,
                    "queue_size": self.command_queue.qsize()
                }
                return CommandResult(True, "State retrieved", data=state_info)
            else:
                return CommandResult(False, f"Unknown command: {command}")

        except Exception as e:
            return CommandResult(False, f"Error executing command: {e}", error=str(e))

    def can_handle(self, command: str) -> bool:
        """Проверка, может ли модуль обработать команду"""
        voice_commands = ["start_listening", "stop_listening", "listen", "voice", "get_state"]
        return any(cmd in command.lower() for cmd in voice_commands)

    async def cleanup(self):
        """Очистка ресурсов модуля"""
        self.stop_listening()
        await super().cleanup()
