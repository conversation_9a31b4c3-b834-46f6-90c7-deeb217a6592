#!/usr/bin/env python3
"""
Тестирование улучшенного голосового модуля Jarvis
"""

import asyncio
import time
from jarvis.modules.voice import VoiceModule
from jarvis.core.logger import jarvis_logger

class VoiceModuleTester:
    """Класс для тестирования голосового модуля"""
    
    def __init__(self):
        self.logger = jarvis_logger
        self.voice_module = VoiceModule()
        self.commands_received = []
        
    async def test_voice_module(self):
        """Основной тест голосового модуля"""
        self.logger.info("🧪 Starting Voice Module Test")
        
        # Инициализация
        success = await self.voice_module.initialize()
        if not success:
            self.logger.error("❌ Failed to initialize voice module")
            return False
            
        self.logger.info("✅ Voice module initialized successfully")
        
        # Запуск прослушивания
        await self.voice_module.start_listening()
        self.logger.info("🎤 Voice module is listening...")
        
        # Тестирование в течение 30 секунд
        test_duration = 30
        start_time = time.time()
        
        self.logger.info(f"🕐 Testing for {test_duration} seconds...")
        self.logger.info("💬 Try saying:")
        self.logger.info("   • 'Джарвис привет'")
        self.logger.info("   • 'Jarvis открой сафари'")
        self.logger.info("   • 'Джарвис тест'")
        self.logger.info("   • 'Jarvis помощь'")
        
        command_count = 0
        last_state = ""
        
        while time.time() - start_time < test_duration:
            # Проверяем состояние
            current_state = self.voice_module.get_listening_state()
            if current_state != last_state:
                self.logger.info(f"🔄 State changed: {last_state} → {current_state}")
                last_state = current_state
            
            # Проверяем команды
            command = self.voice_module.get_command()
            if command:
                command_count += 1
                self.commands_received.append(command)
                self.logger.info(f"📝 Command #{command_count}: '{command}'")
                
                # Симулируем обработку команды
                await self._simulate_command_processing(command)
            
            await asyncio.sleep(0.1)
        
        # Остановка
        self.voice_module.stop_listening()
        await self.voice_module.cleanup()
        
        # Результаты
        self.logger.info("🏁 Test completed!")
        self.logger.info(f"📊 Commands received: {command_count}")
        
        if self.commands_received:
            self.logger.info("📋 Commands list:")
            for i, cmd in enumerate(self.commands_received, 1):
                self.logger.info(f"   {i}. '{cmd}'")
        else:
            self.logger.warning("⚠️  No commands were received during test")
            
        return command_count > 0
    
    async def _simulate_command_processing(self, command: str):
        """Симуляция обработки команды"""
        self.logger.info(f"🧠 Processing command: '{command}'")
        
        # Простая классификация команд
        command_lower = command.lower()
        
        if any(word in command_lower for word in ["привет", "hello", "hi"]):
            self.logger.info("👋 Greeting command detected")
        elif any(word in command_lower for word in ["открой", "open", "запусти"]):
            self.logger.info("🚀 App open command detected")
        elif any(word in command_lower for word in ["закрой", "close"]):
            self.logger.info("🔴 App close command detected")
        elif any(word in command_lower for word in ["тест", "test"]):
            self.logger.info("✅ Test command detected")
        elif any(word in command_lower for word in ["помощь", "help"]):
            self.logger.info("💡 Help command detected")
        else:
            self.logger.info("❓ Unknown command")
    
    async def test_wake_word_variations(self):
        """Тест различных вариантов wake word"""
        self.logger.info("🧪 Testing wake word variations...")
        
        # Инициализация
        success = await self.voice_module.initialize()
        if not success:
            return False
            
        await self.voice_module.start_listening()
        
        test_phrases = [
            "джарвис привет",
            "jarvis открой терминал", 
            "жарвис тест",
            "дарвис помощь",
            "джарв закрой сафари"
        ]
        
        self.logger.info("🗣️  Try saying these test phrases:")
        for phrase in test_phrases:
            self.logger.info(f"   • '{phrase}'")
        
        # Тест в течение 20 секунд
        start_time = time.time()
        while time.time() - start_time < 20:
            command = self.voice_module.get_command()
            if command:
                self.logger.info(f"✅ Wake word test successful: '{command}'")
            await asyncio.sleep(0.1)
        
        self.voice_module.stop_listening()
        await self.voice_module.cleanup()
        
        return True

async def main():
    """Главная функция тестирования"""
    tester = VoiceModuleTester()
    
    try:
        # Основной тест
        success = await tester.test_voice_module()
        
        if success:
            tester.logger.info("🎉 Voice module test PASSED!")
        else:
            tester.logger.error("❌ Voice module test FAILED!")
            
    except KeyboardInterrupt:
        tester.logger.info("🛑 Test interrupted by user")
    except Exception as e:
        tester.logger.error(f"💥 Test failed with error: {e}")
    finally:
        # Очистка
        if tester.voice_module.is_listening:
            tester.voice_module.stop_listening()
        await tester.voice_module.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
